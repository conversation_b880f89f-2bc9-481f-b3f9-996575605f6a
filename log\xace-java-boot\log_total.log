[2025-06-15 17:24:07.294] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Starting XhAdminApplication using Java 20.0.1 with PID 35996 (G:\v2\51erp\51erp-service\xace-java-boot\xh-admin\target\classes started by Administrator in G:\v2\51erp)
[2025-06-15 17:24:07.295] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - The following 1 profile is active: "vpn"
[2025-06-15 17:24:09.888] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Multiple Spring Data modules found, entering strict repository configuration mode
[2025-06-15 17:24:09.891] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-06-15 17:24:09.916] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[2025-06-15 17:24:10.862] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.875] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.882] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.966] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.984] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.007] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.013] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.017] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.021] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.036] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.039] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.041] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.044] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.048] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.050] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.053] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.055] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.073] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.092] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.094] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.521] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat initialized with port 32000 (http)
[2025-06-15 17:24:11.532] [INFO ] [main] [org.apache.catalina.core.StandardService] [?] [?] - Starting service [Tomcat]
[2025-06-15 17:24:11.533] [INFO ] [main] [org.apache.catalina.core.StandardEngine] [?] [?] - Starting Servlet engine: [Apache Tomcat/10.1.19]
[2025-06-15 17:24:11.616] [INFO ] [main] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring embedded WebApplicationContext
[2025-06-15 17:24:11.616] [INFO ] [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] [?] [?] - Root WebApplicationContext: initialization completed in 4082 ms
[2025-06-15 17:24:12.060] [INFO ] [main] [org.redisson.Version] [?] [?] - Redisson 3.27.2
[2025-06-15 17:24:12.448] [INFO ] [redisson-netty-1-4] [org.redisson.connection.ConnectionsHolder] [?] [?] - 1 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 17:24:12.525] [INFO ] [redisson-netty-1-19] [org.redisson.connection.ConnectionsHolder] [?] [?] - 24 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 17:24:13.501] [INFO ] [main] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1,master} inited
[2025-06-15 17:24:13.502] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource - add a datasource named [master] success
[2025-06-15 17:24:13.503] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
[2025-06-15 17:24:16.043] [ERROR] [main] [c.baomidou.mybatisplus.core.MybatisConfiguration] [?] [?] - mapper[com.xinghuo.allegro.push.dao.PushOfferMapper.pushList1] is ignored, because it exists, maybe from xml file
[2025-06-15 17:24:16.761] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 17:24:16.763] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 17:24:16.768] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 17:24:16.775] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 17:24:16.788] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 17:24:25.149] [WARN ] [main] [o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext] [?] [?] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'permissionAdminAspect': Unsatisfied dependency expressed through field 'organizeRelationService': Error creating bean with name 'organizeRelationServiceImpl': Unsatisfied dependency expressed through field 'roleService': Error creating bean with name 'roleServiceImpl': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userRelationService': Error creating bean with name 'userRelationServiceImpl': Unsatisfied dependency expressed through field 'synThirdInfoService': Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
[2025-06-15 17:24:25.152] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource start closing ....
[2025-06-15 17:24:25.155] [INFO ] [main] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1} closing ...
[2025-06-15 17:24:25.166] [INFO ] [main] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1} closed
[2025-06-15 17:24:25.167] [INFO ] [main] [c.b.d.d.destroyer.DefaultDataSourceDestroyer] [?] [?] - dynamic-datasource close the datasource named [master] success,
[2025-06-15 17:24:25.167] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource all closed success,bye
[2025-06-15 17:24:25.195] [INFO ] [main] [org.apache.catalina.core.StandardService] [?] [?] - Stopping service [Tomcat]
[2025-06-15 17:24:25.221] [INFO ] [main] [o.s.b.a.logging.ConditionEvaluationReportLogger] [?] [?] - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
[2025-06-15 17:24:25.252] [ERROR] [main] [org.springframework.boot.SpringApplication] [?] [?] - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'permissionAdminAspect': Unsatisfied dependency expressed through field 'organizeRelationService': Error creating bean with name 'organizeRelationServiceImpl': Unsatisfied dependency expressed through field 'roleService': Error creating bean with name 'roleServiceImpl': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userRelationService': Error creating bean with name 'userRelationServiceImpl': Unsatisfied dependency expressed through field 'synThirdInfoService': Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:962)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at com.xinghuo.admin.XhAdminApplication.main(XhAdminApplication.java:41)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'organizeRelationServiceImpl': Unsatisfied dependency expressed through field 'roleService': Error creating bean with name 'roleServiceImpl': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userRelationService': Error creating bean with name 'userRelationServiceImpl': Unsatisfied dependency expressed through field 'synThirdInfoService': Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 18 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'roleServiceImpl': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userRelationService': Error creating bean with name 'userRelationServiceImpl': Unsatisfied dependency expressed through field 'synThirdInfoService': Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 32 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userRelationService': Error creating bean with name 'userRelationServiceImpl': Unsatisfied dependency expressed through field 'synThirdInfoService': Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 46 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userRelationServiceImpl': Unsatisfied dependency expressed through field 'synThirdInfoService': Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 60 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.checkLookupMethods(AutowiredAnnotationBeanPostProcessor.java:497)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:367)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineConstructorsFromBeanPostProcessors(AbstractAutowireCapableBeanFactory.java:1294)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 74 common frames omitted
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.xinghuo.message.service.impl.SynThirdInfoServiceImpl] from ClassLoader [jdk.internal.loader.ClassLoaders$AppClassLoader@36baf30c]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:483)
	at org.springframework.util.ReflectionUtils.doWithLocalMethods(ReflectionUtils.java:320)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.checkLookupMethods(AutowiredAnnotationBeanPostProcessor.java:475)
	... 87 common frames omitted
Caused by: java.lang.NoClassDefFoundError: com/xinghuo/message/util/SynThirdTotal
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3502)
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2601)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:465)
	... 89 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.xinghuo.message.util.SynThirdTotal
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:521)
	... 93 common frames omitted
[2025-06-15 17:48:19.915] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Starting XhAdminApplication using Java 20.0.1 with PID 31484 (G:\v2\51erp\51erp-service\xace-java-boot\xh-admin\target\classes started by Administrator in G:\v2\51erp)
[2025-06-15 17:48:19.916] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - The following 1 profile is active: "vpn"
[2025-06-15 17:48:22.417] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Multiple Spring Data modules found, entering strict repository configuration mode
[2025-06-15 17:48:22.419] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-06-15 17:48:22.446] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
[2025-06-15 17:48:23.470] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.486] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.494] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.597] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.617] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.632] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.636] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.643] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.647] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.662] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.664] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.667] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.669] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.672] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.674] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.676] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.678] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.696] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.714] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.716] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:24.145] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat initialized with port 32000 (http)
[2025-06-15 17:48:24.158] [INFO ] [main] [org.apache.catalina.core.StandardService] [?] [?] - Starting service [Tomcat]
[2025-06-15 17:48:24.158] [INFO ] [main] [org.apache.catalina.core.StandardEngine] [?] [?] - Starting Servlet engine: [Apache Tomcat/10.1.19]
[2025-06-15 17:48:24.238] [INFO ] [main] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring embedded WebApplicationContext
[2025-06-15 17:48:24.238] [INFO ] [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] [?] [?] - Root WebApplicationContext: initialization completed in 4114 ms
[2025-06-15 17:48:24.697] [INFO ] [main] [org.redisson.Version] [?] [?] - Redisson 3.27.2
[2025-06-15 17:48:25.104] [INFO ] [redisson-netty-1-4] [org.redisson.connection.ConnectionsHolder] [?] [?] - 1 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 17:48:25.169] [INFO ] [redisson-netty-1-19] [org.redisson.connection.ConnectionsHolder] [?] [?] - 24 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 17:48:26.106] [INFO ] [main] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1,master} inited
[2025-06-15 17:48:26.107] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource - add a datasource named [master] success
[2025-06-15 17:48:26.107] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
[2025-06-15 17:48:27.343] [ERROR] [main] [c.baomidou.mybatisplus.core.MybatisConfiguration] [?] [?] - mapper[com.xinghuo.allegro.push.dao.PushOfferMapper.pushList1] is ignored, because it exists, maybe from xml file
[2025-06-15 17:48:27.641] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 17:48:27.642] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 17:48:27.643] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 17:48:27.644] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 17:48:27.647] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 17:48:54.891] [INFO ] [main] [com.xinghuo.fruugo.collect.util.FruugoSkuInfoUtil] [?] [?] - 注入FruugoImageService成功
[2025-06-15 17:48:54.891] [INFO ] [main] [com.xinghuo.fruugo.collect.util.FruugoSkuInfoUtil] [?] [?] - 设置allgeroUrl: http://127.0.0.1:3100/staticImage/
[2025-06-15 17:49:01.928] [INFO ] [main] [c.a.jetcache.autoconfigure.AbstractCacheAutoInit] [?] [?] - init cache area default , type= linkedhashmap
[2025-06-15 17:49:01.934] [INFO ] [main] [c.a.jetcache.autoconfigure.AbstractCacheAutoInit] [?] [?] - init cache area default , type= redis.springdata
[2025-06-15 17:49:01.960] [INFO ] [main] [com.alicp.jetcache.support.DefaultMetricsManager] [?] [?] - cache stat period at 15 MINUTES
[2025-06-15 17:49:01.987] [INFO ] [main] [c.a.j.redis.springdata.SpringDataBroadcastManager] [?] [?] - create RedisMessageListenerContainer instance
[2025-06-15 17:49:02.011] [INFO ] [main] [c.a.j.redis.springdata.SpringDataBroadcastManager] [?] [?] - subscribe jetcache invalidate notification. channel=projectA
[2025-06-15 17:49:10.771] [INFO ] [main] [c.xinghuo.common.database.config.IdGeneratorConfig] [?] [?] - 当前ID生成器编号: 730
[2025-06-15 17:49:11.580] [INFO ] [main] [com.xinghuo.scheduletask.config.XxlJobConfig] [?] [?] - >>>>>>>>>>> xxl-job config init.
[2025-06-15 17:49:11.631] [INFO ] [main] [o.d.x.file.storage.core.FileStorageServiceBuilder] [?] [?] - 加载本地升级版存储平台：local-plus-1
[2025-06-15 17:49:12.959] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Using default implementation for ThreadExecutor
[2025-06-15 17:49:12.970] [INFO ] [main] [org.quartz.core.SchedulerSignalerImpl] [?] [?] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[2025-06-15 17:49:12.971] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Quartz Scheduler v.2.3.2 created.
[2025-06-15 17:49:12.971] [INFO ] [main] [org.quartz.simpl.RAMJobStore] [?] [?] - RAMJobStore initialized.
[2025-06-15 17:49:12.971] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[2025-06-15 17:49:12.971] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
[2025-06-15 17:49:12.971] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Quartz scheduler version: 2.3.2
[2025-06-15 17:49:12.971] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@43e086f2
[2025-06-15 17:49:13.781] [INFO ] [main] [o.s.b.actuate.endpoint.web.EndpointLinksResolver] [?] [?] - Exposing 14 endpoint(s) beneath base path '/actuator'
[2025-06-15 17:49:15.177] [INFO ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job register jobhandler success, name:defaultHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@65fc21f4[class com.xinghuo.scheduletask.task.ScheduleTaskHandler#defaultHandler]
[2025-06-15 17:49:15.648] [INFO ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job registry-remove success, registryParam:{defaultHandler=com.xxl.job.core.handler.impl.MethodJobHandler@65fc21f4[class com.xinghuo.scheduletask.task.ScheduleTaskHandler#defaultHandler]}, registryResult:ReturnT [code=200, msg=null, content=null]
[2025-06-15 17:49:15.649] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 17:49:15.701] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat started on port 32000 (http) with context path ''
[2025-06-15 17:49:15.702] [INFO ] [main] [o.s.scheduling.quartz.SchedulerFactoryBean] [?] [?] - Starting Quartz Scheduler now
[2025-06-15 17:49:15.702] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
[2025-06-15 17:49:15.722] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Started XhAdminApplication in 56.352 seconds (process running for 57.182)
[2025-06-15 17:49:15.805] [INFO ] [Thread-12] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
[2025-06-15 17:49:16.323] [INFO ] [RMI TCP Connection(5)-**************] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-06-15 17:49:16.323] [INFO ] [RMI TCP Connection(5)-**************] [org.springframework.web.servlet.DispatcherServlet] [?] [?] - Initializing Servlet 'dispatcherServlet'
[2025-06-15 17:49:16.326] [INFO ] [RMI TCP Connection(5)-**************] [org.springframework.web.servlet.DispatcherServlet] [?] [?] - Completed initialization in 3 ms
[2025-06-15 17:49:18.814] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:49:51.815] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:50:00.035] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 17:50:00.036] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 17:50:00.657] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20000} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 17:20:00.036')
[2025-06-15 17:50:00.801] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 17:50:00.801] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [JustBought数据缺失] [JustBought数据]: 失败
[2025-06-15 17:50:00.907] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20001} executed. insert into zz_fruugo_alert_log (id, alert_type, alert_module, alert_content, alert_time
	, status, notify_type, ip, created_at, updated_at)
values ('703991272129476229', 3, 'collect_sale', '最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行', TIMESTAMP '2025-06-15 17:50:00.674'
	, 0, 1, '***********', TIMESTAMP '2025-06-15 17:50:00.674', TIMESTAMP '2025-06-15 17:50:00.674')
[2025-06-15 17:50:00.908] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 17:50:00.943] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20002} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 15:50:00.908'
	and country = 'CN')
[2025-06-15 17:50:00.984] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20003} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 17:50:00.986] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 17:50:00.986] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [产品数据堆积] [产品数据]: 失败
[2025-06-15 17:50:01.059] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20004} executed. insert into zz_fruugo_alert_log (id, alert_type, alert_module, alert_content, alert_time
	, status, notify_type, ip, created_at, updated_at)
values ('703991272905422469', 5, 'product', '产品数据堆积严重，当前有 30988 条待处理产品数据，超过阈值(10000)，请检查产品处理系统', TIMESTAMP '2025-06-15 17:50:00.986'
	, 0, 1, '***********', TIMESTAMP '2025-06-15 17:50:00.986', TIMESTAMP '2025-06-15 17:50:00.986')
[2025-06-15 17:50:01.060] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30988 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 17:50:01.077] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20005} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 17:50:01.078] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 17:50:01.078] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 17:50:24.823] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:50:57.835] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:51:30.854] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:52:03.877] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:52:36.895] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:53:09.908] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:53:42.915] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:54:15.924] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:54:48.941] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:55:00.009] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 17:55:00.009] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 17:55:00.133] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20006} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 17:25:00.009')
[2025-06-15 17:55:00.134] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 17:55:00.134] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 17:55:00.218] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20007} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 15:55:00.134'
	and country = 'CN')
[2025-06-15 17:55:18.893] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20008} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 17:55:18.893] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 17:55:18.893] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30980 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 17:55:18.943] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20009} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 17:55:18.944] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 17:55:18.944] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 17:55:21.947] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:55:54.968] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:56:27.986] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:57:00.987] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:57:34.003] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:58:07.020] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:58:40.038] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:59:13.059] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:59:46.077] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:00:00.011] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - 开始执行Fruugo监控数据定时刷新任务
[2025-06-15 18:00:00.012] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 开始生成 2025-06-15 的监控数据
[2025-06-15 18:00:00.027] [INFO ] [JetCacheDefaultExecutor] [com.alicp.jetcache.support.StatInfoLogger] [?] [?] - jetcache stat from 2025-06-15 17:49:01,959 to 2025-06-15 18:00:00,013
cache           |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
xaceCache       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

[2025-06-15 18:00:00.060] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20010} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where (stat_date = '2025-06-15')
[2025-06-15 18:00:02.530] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20011} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_offer
[2025-06-15 18:00:03.604] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20012} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 0)
[2025-06-15 18:00:08.491] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20013} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 2)
[2025-06-15 18:00:19.091] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:00:23.639] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20014} executed. select count(*) as total
from zz_fruugo_collect_offer
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:00:52.115] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:00:56.532] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20015} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_sale
[2025-06-15 18:00:56.551] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20016} executed. select count(*) as total
from zz_fruugo_collect_sale
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:00:56.942] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20017} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_seller
[2025-06-15 18:00:56.962] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20018} executed. select count(*) as total
from zz_fruugo_seller
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:01:07.119] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20019} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product
[2025-06-15 18:01:25.142] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:01:27.849] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20020} executed. select count(*) as total
from zz_fruugo_product
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:01:58.146] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:02:31.165] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:02:42.152] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20021} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product_sku
[2025-06-15 18:03:04.184] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:03:37.205] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:04:10.222] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:04:15.663] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20022} executed. select count(*) as total
from zz_fruugo_product_sku
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:04:15.687] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20023} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where id = '703721994465583173'
[2025-06-15 18:04:15.732] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20024} executed. update zz_fruugo_monitor
set stat_date = TIMESTAMP '2025-06-15 00:00:00.000', collect_offer_total = 581232, collect_offer_pending = 120267, collect_offer_processed = 371647, collect_offer_new = 0, collect_sale_total = 8180610, collect_sale_new = 0, seller_total = 2960, seller_new = 0, product_total = 1032438, product_new = 0, sku_total = 2684538, sku_new = 0, f_created_at = TIMESTAMP '2025-06-15 00:00:00.000', f_last_updated_at = TIMESTAMP '2025-06-15 18:04:15.665'
where id = '703721994465583173'
[2025-06-15 18:04:15.733] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 生成 2025-06-15 的监控数据 成功
[2025-06-15 18:04:15.733] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo基础监控数据刷新 成功
[2025-06-15 18:04:15.733] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成今日的Fruugo数据监控信息
[2025-06-15 18:04:15.733] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成指定日期的Fruugo数据监控信息，日期: 2025-06-15
[2025-06-15 18:04:15.734] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 开始生成 2025-06-15 的监控数据
[2025-06-15 18:04:15.748] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20025} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where (stat_date = '2025-06-15')
[2025-06-15 18:04:17.788] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20026} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_offer
[2025-06-15 18:04:17.945] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20027} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 0)
[2025-06-15 18:04:18.345] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20028} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 2)
[2025-06-15 18:04:20.211] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20029} executed. select count(*) as total
from zz_fruugo_collect_offer
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:04:43.244] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:04:45.618] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20030} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_sale
[2025-06-15 18:04:45.632] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20031} executed. select count(*) as total
from zz_fruugo_collect_sale
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:04:45.715] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20032} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_seller
[2025-06-15 18:04:45.734] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20033} executed. select count(*) as total
from zz_fruugo_seller
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:04:54.032] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20034} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product
[2025-06-15 18:04:57.491] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20035} executed. select count(*) as total
from zz_fruugo_product
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:05:16.263] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:05:49.278] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:05:58.000] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20036} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product_sku
[2025-06-15 18:06:22.304] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:06:55.317] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:07:13.068] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20037} executed. select count(*) as total
from zz_fruugo_product_sku
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:07:13.099] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20038} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where id = '703721994465583173'
[2025-06-15 18:07:13.125] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20039} executed. update zz_fruugo_monitor
set stat_date = TIMESTAMP '2025-06-15 00:00:00.000', collect_offer_total = 581232, collect_offer_pending = 120267, collect_offer_processed = 371647, collect_offer_new = 0, collect_sale_total = 8180610, collect_sale_new = 0, seller_total = 2960, seller_new = 0, product_total = 1032438, product_new = 0, sku_total = 2684538, sku_new = 0, f_created_at = TIMESTAMP '2025-06-15 00:00:00.000', f_last_updated_at = TIMESTAMP '2025-06-15 18:07:13.069'
where id = '703721994465583173'
[2025-06-15 18:07:13.125] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 生成 2025-06-15 的监控数据 成功
[2025-06-15 18:07:13.125] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成指定日期的Fruugo数据监控信息成功，日期: 2025-06-15
[2025-06-15 18:07:13.125] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo数据监控信息刷新 成功
[2025-06-15 18:07:13.125] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo监控数据定时刷新任务执行完成
[2025-06-15 18:07:13.126] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:07:13.126] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:07:13.212] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20040} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 17:37:13.126')
[2025-06-15 18:07:13.213] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:07:13.213] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:07:13.230] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20041} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:07:13.213'
	and country = 'CN')
[2025-06-15 18:07:20.475] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20042} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:07:20.476] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:07:20.476] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30985 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:07:20.488] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20043} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:07:20.489] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:07:20.489] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:07:28.319] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:07:36.541] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
[2025-06-15 18:07:36.582] [INFO ] [SpringApplicationShutdownHook] [o.s.scheduling.quartz.SchedulerFactoryBean] [?] [?] - Shutting down Quartz Scheduler
[2025-06-15 18:07:36.582] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
[2025-06-15 18:07:36.582] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
[2025-06-15 18:07:36.582] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
[2025-06-15 18:07:36.584] [INFO ] [SpringApplicationShutdownHook] [o.dromara.x.file.storage.core.FileStorageService] [?] [?] - 销毁存储平台 local-plus-1 成功
[2025-06-15 18:07:36.585] [INFO ] [Thread-12] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server stop.
[2025-06-15 18:07:39.600] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:07:39.600] [INFO ] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.thread.ExecutorRegistryThread] [?] [?] - >>>>>>>>>>> xxl-job registry-remove fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='xxl-job-executor-sample1', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connect timed out), for url : http://101.33.227.194:30020/xxl-job-admin/api/registryRemove, content=null]
[2025-06-15 18:07:39.600] [INFO ] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.thread.ExecutorRegistryThread] [?] [?] - >>>>>>>>>>> xxl-job, executor registry thread destroy.
[2025-06-15 18:07:39.600] [INFO ] [SpringApplicationShutdownHook] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server destroy success.
[2025-06-15 18:07:39.600] [INFO ] [xxl-job, executor JobLogFileCleanThread] [com.xxl.job.core.thread.JobLogFileCleanThread] [?] [?] - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
[2025-06-15 18:07:39.601] [INFO ] [xxl-job, executor TriggerCallbackThread] [com.xxl.job.core.thread.TriggerCallbackThread] [?] [?] - >>>>>>>>>>> xxl-job, executor callback thread destroy.
[2025-06-15 18:07:39.601] [INFO ] [Thread-11] [com.xxl.job.core.thread.TriggerCallbackThread] [?] [?] - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
[2025-06-15 18:07:39.602] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 18:07:41.613] [INFO ] [SpringApplicationShutdownHook] [com.alicp.jetcache.support.DefaultMetricsManager] [?] [?] - cache stat canceled
[2025-06-15 18:07:41.649] [INFO ] [SpringApplicationShutdownHook] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource start closing ....
[2025-06-15 18:07:41.651] [INFO ] [SpringApplicationShutdownHook] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1} closing ...
[2025-06-15 18:07:41.654] [INFO ] [SpringApplicationShutdownHook] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1} closed
[2025-06-15 18:07:41.654] [INFO ] [SpringApplicationShutdownHook] [c.b.d.d.destroyer.DefaultDataSourceDestroyer] [?] [?] - dynamic-datasource close the datasource named [master] success,
[2025-06-15 18:07:41.654] [INFO ] [SpringApplicationShutdownHook] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource all closed success,bye
[2025-06-15 18:07:43.992] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Starting XhAdminApplication using Java 20.0.1 with PID 11068 (G:\v2\51erp\51erp-service\xace-java-boot\xh-admin\target\classes started by Administrator in G:\v2\51erp)
[2025-06-15 18:07:43.992] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - The following 1 profile is active: "vpn"
[2025-06-15 18:07:46.113] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Multiple Spring Data modules found, entering strict repository configuration mode
[2025-06-15 18:07:46.115] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-06-15 18:07:46.138] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[2025-06-15 18:07:46.925] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:46.937] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:46.945] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.022] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.039] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.053] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.056] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.060] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.063] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.073] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.075] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.077] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.079] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.082] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.083] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.086] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.087] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.098] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.121] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.122] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.514] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat initialized with port 32000 (http)
[2025-06-15 18:07:47.527] [INFO ] [main] [org.apache.catalina.core.StandardService] [?] [?] - Starting service [Tomcat]
[2025-06-15 18:07:47.527] [INFO ] [main] [org.apache.catalina.core.StandardEngine] [?] [?] - Starting Servlet engine: [Apache Tomcat/10.1.19]
[2025-06-15 18:07:47.604] [INFO ] [main] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring embedded WebApplicationContext
[2025-06-15 18:07:47.605] [INFO ] [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] [?] [?] - Root WebApplicationContext: initialization completed in 3422 ms
[2025-06-15 18:07:48.032] [INFO ] [main] [org.redisson.Version] [?] [?] - Redisson 3.27.2
[2025-06-15 18:07:48.377] [INFO ] [redisson-netty-1-4] [org.redisson.connection.ConnectionsHolder] [?] [?] - 1 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 18:07:48.443] [INFO ] [redisson-netty-1-19] [org.redisson.connection.ConnectionsHolder] [?] [?] - 24 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 18:07:49.334] [INFO ] [main] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1,master} inited
[2025-06-15 18:07:49.334] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource - add a datasource named [master] success
[2025-06-15 18:07:49.334] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
[2025-06-15 18:07:50.529] [ERROR] [main] [c.baomidou.mybatisplus.core.MybatisConfiguration] [?] [?] - mapper[com.xinghuo.allegro.push.dao.PushOfferMapper.pushList1] is ignored, because it exists, maybe from xml file
[2025-06-15 18:07:50.856] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 18:07:50.856] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 18:07:50.857] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 18:07:50.859] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 18:07:50.863] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 18:08:18.922] [INFO ] [main] [com.xinghuo.fruugo.collect.util.FruugoSkuInfoUtil] [?] [?] - 注入FruugoImageService成功
[2025-06-15 18:08:18.922] [INFO ] [main] [com.xinghuo.fruugo.collect.util.FruugoSkuInfoUtil] [?] [?] - 设置allgeroUrl: http://127.0.0.1:3100/staticImage/
[2025-06-15 18:08:26.013] [INFO ] [main] [c.a.jetcache.autoconfigure.AbstractCacheAutoInit] [?] [?] - init cache area default , type= linkedhashmap
[2025-06-15 18:08:26.019] [INFO ] [main] [c.a.jetcache.autoconfigure.AbstractCacheAutoInit] [?] [?] - init cache area default , type= redis.springdata
[2025-06-15 18:08:26.042] [INFO ] [main] [com.alicp.jetcache.support.DefaultMetricsManager] [?] [?] - cache stat period at 15 MINUTES
[2025-06-15 18:08:26.071] [INFO ] [main] [c.a.j.redis.springdata.SpringDataBroadcastManager] [?] [?] - create RedisMessageListenerContainer instance
[2025-06-15 18:08:26.095] [INFO ] [main] [c.a.j.redis.springdata.SpringDataBroadcastManager] [?] [?] - subscribe jetcache invalidate notification. channel=projectA
[2025-06-15 18:08:34.867] [INFO ] [main] [c.xinghuo.common.database.config.IdGeneratorConfig] [?] [?] - 当前ID生成器编号: 731
[2025-06-15 18:08:35.638] [INFO ] [main] [com.xinghuo.scheduletask.config.XxlJobConfig] [?] [?] - >>>>>>>>>>> xxl-job config init.
[2025-06-15 18:08:35.688] [INFO ] [main] [o.d.x.file.storage.core.FileStorageServiceBuilder] [?] [?] - 加载本地升级版存储平台：local-plus-1
[2025-06-15 18:08:37.066] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Using default implementation for ThreadExecutor
[2025-06-15 18:08:37.076] [INFO ] [main] [org.quartz.core.SchedulerSignalerImpl] [?] [?] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[2025-06-15 18:08:37.076] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Quartz Scheduler v.2.3.2 created.
[2025-06-15 18:08:37.077] [INFO ] [main] [org.quartz.simpl.RAMJobStore] [?] [?] - RAMJobStore initialized.
[2025-06-15 18:08:37.077] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[2025-06-15 18:08:37.077] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
[2025-06-15 18:08:37.078] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Quartz scheduler version: 2.3.2
[2025-06-15 18:08:37.078] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@46109086
[2025-06-15 18:08:37.972] [INFO ] [main] [o.s.b.actuate.endpoint.web.EndpointLinksResolver] [?] [?] - Exposing 14 endpoint(s) beneath base path '/actuator'
[2025-06-15 18:08:39.332] [INFO ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job register jobhandler success, name:defaultHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@36959928[class com.xinghuo.scheduletask.task.ScheduleTaskHandler#defaultHandler]
[2025-06-15 18:08:39.822] [INFO ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job registry-remove success, registryParam:{defaultHandler=com.xxl.job.core.handler.impl.MethodJobHandler@36959928[class com.xinghuo.scheduletask.task.ScheduleTaskHandler#defaultHandler]}, registryResult:ReturnT [code=200, msg=null, content=null]
[2025-06-15 18:08:39.825] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 18:08:39.869] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat started on port 32000 (http) with context path ''
[2025-06-15 18:08:39.870] [INFO ] [main] [o.s.scheduling.quartz.SchedulerFactoryBean] [?] [?] - Starting Quartz Scheduler now
[2025-06-15 18:08:39.870] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
[2025-06-15 18:08:39.889] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Started XhAdminApplication in 56.487 seconds (process running for 57.243)
[2025-06-15 18:08:39.981] [INFO ] [Thread-12] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
[2025-06-15 18:08:40.140] [INFO ] [RMI TCP Connection(2)-**************] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-06-15 18:08:40.140] [INFO ] [RMI TCP Connection(2)-**************] [org.springframework.web.servlet.DispatcherServlet] [?] [?] - Initializing Servlet 'dispatcherServlet'
[2025-06-15 18:08:40.143] [INFO ] [RMI TCP Connection(2)-**************] [org.springframework.web.servlet.DispatcherServlet] [?] [?] - Completed initialization in 3 ms
[2025-06-15 18:08:42.990] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:09:16.000] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:09:49.021] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:09:52.804] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20000} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:09:52.815] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:09:52.828] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=624 ms, AverageTime=624 ms

[2025-06-15 18:10:00.018] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:10:00.019] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:10:00.321] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20001} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 17:40:00.019')
[2025-06-15 18:10:00.457] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:10:00.457] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [JustBought数据缺失] [JustBought数据]: 失败
[2025-06-15 18:10:00.541] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20002} executed. insert into zz_fruugo_alert_log (id, alert_type, alert_module, alert_content, alert_time
	, status, notify_type, ip, created_at, updated_at)
values ('703996303851435717', 3, 'collect_sale', '最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行', TIMESTAMP '2025-06-15 18:10:00.323'
	, 0, 1, '***********', TIMESTAMP '2025-06-15 18:10:00.323', TIMESTAMP '2025-06-15 18:10:00.323')
[2025-06-15 18:10:00.543] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:10:00.560] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20003} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:10:00.543'
	and country = 'CN')
[2025-06-15 18:10:00.584] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20004} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:10:00.585] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:10:00.585] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [产品数据堆积] [产品数据]: 失败
[2025-06-15 18:10:00.657] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20005} executed. insert into zz_fruugo_alert_log (id, alert_type, alert_module, alert_content, alert_time
	, status, notify_type, ip, created_at, updated_at)
values ('703996304388306629', 5, 'product', '产品数据堆积严重，当前有 30985 条待处理产品数据，超过阈值(10000)，请检查产品处理系统', TIMESTAMP '2025-06-15 18:10:00.585'
	, 0, 1, '***********', TIMESTAMP '2025-06-15 18:10:00.585', TIMESTAMP '2025-06-15 18:10:00.585')
[2025-06-15 18:10:00.657] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30985 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:10:00.670] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20006} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:10:00.671] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:10:00.671] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:10:01.916] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20007} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:10:01.917] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:10:01.917] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=21 ms, AverageTime=21 ms

[2025-06-15 18:10:11.895] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20008} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:10:11.896] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:10:11.896] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=23 ms, AverageTime=23 ms

[2025-06-15 18:10:21.911] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20009} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:10:21.912] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:10:21.912] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=22 ms, AverageTime=22 ms

[2025-06-15 18:10:22.029] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:10:31.895] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20010} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:10:31.895] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:10:31.896] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=18 ms, AverageTime=18 ms

[2025-06-15 18:10:41.906] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20011} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:10:41.907] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:10:41.908] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=19 ms, AverageTime=19 ms

[2025-06-15 18:10:51.900] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20012} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:10:51.901] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:10:51.901] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=20 ms, AverageTime=20 ms

[2025-06-15 18:10:55.052] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:11:01.908] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20013} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:11:01.908] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:11:01.909] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=21 ms, AverageTime=21 ms

[2025-06-15 18:11:11.906] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20014} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:11:11.908] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:11:11.908] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=17 ms, AverageTime=17 ms

[2025-06-15 18:11:21.898] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20015} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:11:21.899] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:11:21.900] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=19 ms, AverageTime=19 ms

[2025-06-15 18:11:28.074] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:11:31.905] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20016} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:11:31.906] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:11:31.906] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=18 ms, AverageTime=18 ms

[2025-06-15 18:11:41.901] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20017} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:11:41.902] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:11:41.902] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=17 ms, AverageTime=17 ms

[2025-06-15 18:11:46.646] [ERROR] [http-nio-32000-exec-3] [com.xinghuo.exception.exception.ResultException] [?] [?] - 系统异常:No static resource api/amazon/listTask/waitGets.
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource api/amazon/listTask/waitGets.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 18:11:52.047] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20018} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:11:52.047] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:11:52.048] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=169 ms, AverageTime=169 ms

[2025-06-15 18:12:01.090] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:12:01.895] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20019} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:01.895] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:01.895] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=21 ms, AverageTime=21 ms

[2025-06-15 18:12:10.094] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20020} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:10.095] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:10.095] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=22 ms, AverageTime=22 ms

[2025-06-15 18:12:20.094] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20021} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:20.094] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:20.094] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=19 ms, AverageTime=19 ms

[2025-06-15 18:12:24.560] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20022} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:24.561] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:24.561] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=26 ms, AverageTime=26 ms

[2025-06-15 18:12:30.107] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20023} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:30.109] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:30.109] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=23 ms, AverageTime=23 ms

[2025-06-15 18:12:34.107] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:12:40.116] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20024} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:40.117] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:40.117] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=21 ms, AverageTime=21 ms

[2025-06-15 18:12:50.122] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20025} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:50.122] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:50.122] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=35 ms, AverageTime=35 ms

[2025-06-15 18:12:59.390] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20026} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:59.390] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:59.391] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=19 ms, AverageTime=19 ms

[2025-06-15 18:12:59.426] [ERROR] [http-nio-32000-exec-3] [com.xinghuo.exception.exception.ResultException] [?] [?] - 系统异常:No static resource favicon.ico.
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 18:13:00.093] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20027} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:00.093] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:13:00.095] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=25 ms, AverageTime=25 ms

[2025-06-15 18:13:07.133] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:13:10.129] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20028} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:10.130] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:13:10.130] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=21 ms, AverageTime=21 ms

[2025-06-15 18:13:20.103] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20029} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:20.103] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:13:20.105] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=17 ms, AverageTime=17 ms

[2025-06-15 18:13:30.103] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20030} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:30.134] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20031} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A274332011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 99999, status = 1, max_pages_to_crawl = 1, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 16:25:33.000', updated_at = TIMESTAMP '2025-06-15 18:13:30.106'
where id = 4112
[2025-06-15 18:13:30.135] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 4112
[2025-06-15 18:13:30.148] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 18:13:40.103] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20032} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:40.127] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20033} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2528084011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2528084011, status = 1, max_pages_to_crawl = 400, crawled_pages = 4, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:13:40.104', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Centerpieces(核心)'
where id = 1
[2025-06-15 18:13:40.127] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 1
[2025-06-15 18:13:40.127] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:13:40.147] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:13:50.105] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20034} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:50.130] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20035} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A274321011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 274321011, status = 1, max_pages_to_crawl = 400, crawled_pages = 9, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:13:50.106', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Decorations(装饰品)/Balloons(气球)'
where id = 2
[2025-06-15 18:13:50.130] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 2
[2025-06-15 18:13:50.130] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:13:59.380] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20036} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:59.402] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20037} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2489384011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2489384011, status = 1, max_pages_to_crawl = 400, crawled_pages = 6, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:13:59.380', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Decorations(装饰品)/Banners(横幅)'
where id = 3
[2025-06-15 18:13:59.403] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 3
[2025-06-15 18:13:59.403] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=40 ms, AverageTime=40 ms

[2025-06-15 18:13:59.436] [ERROR] [http-nio-32000-exec-3] [com.xinghuo.exception.exception.ResultException] [?] [?] - 系统异常:No static resource favicon.ico.
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 18:14:00.099] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20038} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:14:00.122] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20039} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10844431011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10844431011, status = 1, max_pages_to_crawl = 400, crawled_pages = 6, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:14:00.099', source_category_path = 'Home & Kitchen/Event & Party Supplies/Children''s Party Supplies/Decorations/Cake & Cupcake Toppers/Non-Edible Cake Toppers'
where id = 4
[2025-06-15 18:14:00.122] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 4
[2025-06-15 18:14:00.123] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:14:10.108] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20040} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:14:10.131] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20041} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10844432011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10844432011, status = 1, max_pages_to_crawl = 400, crawled_pages = 2, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:14:10.109', source_category_path = 'Home & Kitchen/Event & Party Supplies/Children''s Party Supplies/Decorations/Cake & Cupcake Toppers/Non-Edible Cupcake Toppers'
where id = 5
[2025-06-15 18:14:10.132] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 5
[2025-06-15 18:14:10.132] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:14:13.160] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:14:20.094] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20042} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:14:20.118] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20043} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A723472011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 723472011, status = 1, max_pages_to_crawl = 400, crawled_pages = 5, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:14:20.095', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Decorations(装饰品)/Streamers(拖缆)'
where id = 6
[2025-06-15 18:14:20.119] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 6
[2025-06-15 18:14:20.119] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:14:28.246] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20044} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:14:28.270] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20045} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A274325011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 274325011, status = 1, max_pages_to_crawl = 400, crawled_pages = 8, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:14:28.248', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Headwear(头饰)'
where id = 7
[2025-06-15 18:14:28.270] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 7
[2025-06-15 18:14:28.270] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:14:30.146] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20046} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:14:30.175] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20047} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2528081011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2528081011, status = 1, max_pages_to_crawl = 400, crawled_pages = 3, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:14:30.148', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Invitations(邀请)'
where id = 8
[2025-06-15 18:14:30.175] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 8
[2025-06-15 18:14:30.175] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 18:14:40.104] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20048} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:14:40.128] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20049} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A166042011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 166042011, status = 1, max_pages_to_crawl = 400, crawled_pages = 5, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:14:40.105', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Noisemakers(噪音制造者)'
where id = 9
[2025-06-15 18:14:40.128] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 9
[2025-06-15 18:14:40.128] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:14:46.178] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:14:50.093] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20050} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:14:50.120] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20051} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A23569992011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 23569992011, status = 1, max_pages_to_crawl = 400, crawled_pages = 6, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:14:50.094', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Party Favors(派对恩惠)/Multi-Item Party Favor Packs(多件派对优惠包)'
where id = 10
[2025-06-15 18:14:50.120] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 10
[2025-06-15 18:14:50.121] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:15:00.006] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:15:00.006] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:15:00.014] [INFO ] [JetCacheDefaultExecutor] [com.alicp.jetcache.support.StatInfoLogger] [?] [?] - jetcache stat from 2025-06-15 18:08:26,042 to 2025-06-15 18:15:00,006
cache           |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
xaceCache       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

[2025-06-15 18:15:00.086] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20052} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 17:45:00.006')
[2025-06-15 18:15:00.087] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:15:00.087] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:15:00.118] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20053} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:15:00.087'
	and country = 'CN')
[2025-06-15 18:15:00.159] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20054} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:15:00.159] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:15:00.159] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31006 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:15:00.165] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20055} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:15:00.172] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20056} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:15:00.172] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:15:00.172] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:15:00.191] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20057} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A274331011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 274331011, status = 1, max_pages_to_crawl = 400, crawled_pages = 4, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:15:00.166', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Party Tableware(派对餐具)/Cups(杯子)'
where id = 11
[2025-06-15 18:15:00.191] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 11
[2025-06-15 18:15:00.192] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:15:10.115] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20058} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:15:10.142] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20059} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A274332011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 274332011, status = 1, max_pages_to_crawl = 400, crawled_pages = 6, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:15:10.116', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Party Tableware(派对餐具)/Napkins(餐巾)'
where id = 12
[2025-06-15 18:15:10.142] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 12
[2025-06-15 18:15:10.142] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:15:19.201] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:15:20.103] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20060} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:15:20.126] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20061} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A274333011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 274333011, status = 1, max_pages_to_crawl = 400, crawled_pages = 8, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:15:20.104', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Party Tableware(派对餐具)/Plates(板)'
where id = 13
[2025-06-15 18:15:20.126] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 13
[2025-06-15 18:15:20.126] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=40 ms, AverageTime=40 ms

[2025-06-15 18:15:30.126] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20062} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:15:30.152] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20063} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A274329011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 274329011, status = 1, max_pages_to_crawl = 400, crawled_pages = 2, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:15:30.128', source_category_path = 'Home & Kitchen/Event & Party Supplies/Children''s Party Supplies/Piñatas'
where id = 14
[2025-06-15 18:15:30.152] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 14
[2025-06-15 18:15:30.154] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:15:40.091] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20064} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:15:40.113] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20065} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2528083011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2528083011, status = 1, max_pages_to_crawl = 400, crawled_pages = 6, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:15:40.092', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Tablecovers(桌布)'
where id = 15
[2025-06-15 18:15:40.114] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 15
[2025-06-15 18:15:40.114] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:15:50.105] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20066} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:15:50.135] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20067} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A17928788011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17928788011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:15:50.107', source_category_path = 'Handmade Products(手工产品)/Sports & Outdoors(运动与户外)/Camping & Hiking(露营和徒步旅行)/Water Bottles & Accessories(水瓶和配件)'
where id = 16
[2025-06-15 18:15:50.136] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 16
[2025-06-15 18:15:50.136] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:15:52.215] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:16:00.103] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20068} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:16:00.152] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20069} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A14351409011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14351409011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:16:00.106', source_category_path = 'Handmade Products(手工产品)/Sports & Outdoors(运动与户外)/Cycling(骑自行车)/Cycling Accessories(自行车配件)'
where id = 17
[2025-06-15 18:16:00.152] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 17
[2025-06-15 18:16:00.152] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=70 ms, AverageTime=70 ms

[2025-06-15 18:16:10.100] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20070} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:16:10.124] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20071} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A17928772011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17928772011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:16:10.101', source_category_path = 'Handmade Products(手工产品)/Sports & Outdoors(运动与户外)/Hunting & Shooting(狩猎和射击)/Archery(射箭)/Arrows & Accessories(箭头和附件)'
where id = 18
[2025-06-15 18:16:10.125] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 18
[2025-06-15 18:16:10.125] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:16:20.100] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20072} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:16:20.127] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20073} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A17928778011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17928778011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:16:20.101', source_category_path = 'Handmade Products(手工产品)/Sports & Outdoors(运动与户外)/Hunting & Shooting(狩猎和射击)/Gun Accessories(枪支附件)/Gun & Ammunition Storage(枪支弹药储存)'
where id = 19
[2025-06-15 18:16:20.127] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 19
[2025-06-15 18:16:20.127] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:16:25.233] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:16:30.089] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20074} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:16:30.122] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20075} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A17928788011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17928788011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:16:30.090', source_category_path = 'Handmade Products(手工产品)/Sports & Outdoors(运动与户外)/Sports & Fitness(运动与健身)/Accessories(附件)/Water Bottles & Accessories(水瓶和配件)'
where id = 20
[2025-06-15 18:16:30.122] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 20
[2025-06-15 18:16:30.122] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:16:40.113] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20076} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:16:40.141] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20077} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A17928795011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17928795011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:16:40.115', source_category_path = 'Handmade Products(手工产品)/Sports & Outdoors(运动与户外)/Sports & Fitness(运动与健身)/Golf(高尔夫)/Balls & Accessories(球和配件)'
where id = 21
[2025-06-15 18:16:40.142] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 21
[2025-06-15 18:16:40.142] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:16:50.106] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20078} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:16:50.138] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20079} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A15970507011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15970507011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:16:50.107', source_category_path = 'Handmade Products(手工产品)/Beauty & Grooming(美容与美容)/Tools & Accessories(工具和附件)/Storage & Organization(存储和组织)/Makeup Organizers(化妆组织者)'
where id = 22
[2025-06-15 18:16:50.138] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 22
[2025-06-15 18:16:50.138] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:16:58.247] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:17:00.106] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20080} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:17:00.144] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20081} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732451%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732451, status = 1, max_pages_to_crawl = 400, crawled_pages = 1, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:17:00.107', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Air Mattresses & Accessories(空气床垫和配件)/Air Mattresses(空气床垫)'
where id = 23
[2025-06-15 18:17:00.144] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 23
[2025-06-15 18:17:00.144] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=64 ms, AverageTime=64 ms

[2025-06-15 18:17:10.103] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20082} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:17:10.128] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20083} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732481%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732481, status = 1, max_pages_to_crawl = 400, crawled_pages = 3, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:17:10.104', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Air Mattresses & Accessories(空气床垫和配件)/Pumps(泵)'
where id = 24
[2025-06-15 18:17:10.129] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 24
[2025-06-15 18:17:10.129] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:17:20.093] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20084} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:17:20.119] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20085} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732201%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732201, status = 1, max_pages_to_crawl = 400, crawled_pages = 3, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:17:20.095', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Canopies & Drapes(床篷和窗帘)'
where id = 25
[2025-06-15 18:17:20.120] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 25
[2025-06-15 18:17:20.120] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:17:30.112] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20086} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:17:30.163] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20087} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671043011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671043011, status = 1, max_pages_to_crawl = 400, crawled_pages = 2, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:17:30.128', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Bed Pillows(枕头)'
where id = 26
[2025-06-15 18:17:30.164] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 26
[2025-06-15 18:17:30.165] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=75 ms, AverageTime=75 ms

[2025-06-15 18:17:31.256] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:17:40.098] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20088} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:17:40.135] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20089} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3731991%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3731991, status = 1, max_pages_to_crawl = 400, crawled_pages = 2, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:17:40.099', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Body Pillows(身体枕头)'
where id = 27
[2025-06-15 18:17:40.135] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 27
[2025-06-15 18:17:40.136] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:17:50.098] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20090} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:17:50.122] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20091} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732061%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732061, status = 1, max_pages_to_crawl = 400, crawled_pages = 1, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:17:50.099', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Leg Positioner Pillows(腿部定位器枕头)'
where id = 28
[2025-06-15 18:17:50.122] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 28
[2025-06-15 18:17:50.122] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:18:00.100] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20092} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:18:00.135] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20093} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732051%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732051, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:18:00.101', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Lumbar Pillows(腰枕)'
where id = 29
[2025-06-15 18:18:00.135] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 29
[2025-06-15 18:18:00.136] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=58 ms, AverageTime=58 ms

[2025-06-15 18:18:04.268] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:18:10.105] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20094} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:18:10.137] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20095} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732111%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732111, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:18:10.106', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Neck & Cervical Pillows(颈枕)'
where id = 30
[2025-06-15 18:18:10.137] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 30
[2025-06-15 18:18:10.137] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:18:20.104] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20096} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:18:20.127] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20097} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732021%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732021, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:18:20.105', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Reading & Bed Rest Pillows(阅读和床上休息枕头)'
where id = 31
[2025-06-15 18:18:20.128] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 31
[2025-06-15 18:18:20.128] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:18:30.098] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20098} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:18:30.120] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20099} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732121%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732121, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:18:30.099', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Travel Pillows(旅行枕头)'
where id = 32
[2025-06-15 18:18:30.120] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 32
[2025-06-15 18:18:30.121] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:18:37.278] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:18:40.125] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20100} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:18:40.150] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20101} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3775471%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3775471, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:18:40.126', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Wedges & Body Positioners(楔子和身体定位器)'
where id = 33
[2025-06-15 18:18:40.150] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 33
[2025-06-15 18:18:40.150] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:18:50.103] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20102} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:18:50.128] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20103} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16175638011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16175638011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:18:50.104', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Runners & Scarves(床跑者和围巾)'
where id = 34
[2025-06-15 18:18:50.128] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 34
[2025-06-15 18:18:50.128] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:19:00.103] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20104} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:19:00.136] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20105} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732211%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732211, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:19:00.104', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Skirts(床裙)'
where id = 35
[2025-06-15 18:19:00.136] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 35
[2025-06-15 18:19:00.137] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 18:19:10.091] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20106} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:19:10.122] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20107} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732271%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732271, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:19:10.091', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedding Accessories(床上用品配件)/Bed Risers(床板)'
where id = 36
[2025-06-15 18:19:10.122] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 36
[2025-06-15 18:19:10.122] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:19:10.282] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:19:20.093] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20108} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:19:20.117] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20109} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732311%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732311, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:19:20.093', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedding Accessories(床上用品配件)/Bed Skirt Pins(床裙别针)'
where id = 37
[2025-06-15 18:19:20.117] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 37
[2025-06-15 18:19:20.118] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:19:30.110] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20110} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:19:30.142] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20111} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A21579650011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 21579650011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:19:30.111', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Air Fresheners(空气清新剂)/Charcoal Bags(炭袋)'
where id = 38
[2025-06-15 18:19:30.143] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 38
[2025-06-15 18:19:30.143] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 18:19:40.109] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20112} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:19:40.133] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20113} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874218011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874218011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:19:40.111', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedding Accessories(床上用品配件)/Comforter Clips(被子夹)'
where id = 39
[2025-06-15 18:19:40.133] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 39
[2025-06-15 18:19:40.134] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:19:43.295] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:19:50.124] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20114} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:19:50.149] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20115} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14087331%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14087331, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:19:50.125', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Artificial Flowers'
where id = 40
[2025-06-15 18:19:50.149] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 40
[2025-06-15 18:19:50.149] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:20:00.002] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:20:00.002] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:20:00.084] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20116} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 17:50:00.002')
[2025-06-15 18:20:00.084] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:20:00.085] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:20:00.097] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20117} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:20:00.105] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20118} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:20:00.085'
	and country = 'CN')
[2025-06-15 18:20:00.144] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20119} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732241%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732241, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:20:00.098', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedding Accessories(床上用品配件)/Sheet Fasteners(薄板紧固件)'
where id = 41
[2025-06-15 18:20:00.144] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 41
[2025-06-15 18:20:00.146] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=69 ms, AverageTime=69 ms

[2025-06-15 18:20:00.232] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20120} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:20:00.232] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:20:00.232] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30988 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:20:00.264] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20121} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:20:00.265] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:20:00.265] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:20:10.100] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20122} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:20:10.124] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20123} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14087391%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14087391, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:20:10.101', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Artificial Fruit'
where id = 42
[2025-06-15 18:20:10.124] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 42
[2025-06-15 18:20:10.125] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:20:16.309] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:20:20.110] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20124} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:20:20.140] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20125} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3731671%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3731671, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:20:20.112', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bath Linen Sets(沐浴亚麻套装)'
where id = 43
[2025-06-15 18:20:20.141] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 43
[2025-06-15 18:20:20.141] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=63 ms, AverageTime=63 ms

[2025-06-15 18:20:30.116] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20126} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:20:30.153] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20127} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433413011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433413011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:20:30.117', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Artwork(艺术品)/Drawings(图纸)'
where id = 44
[2025-06-15 18:20:30.154] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 44
[2025-06-15 18:20:30.154] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=68 ms, AverageTime=68 ms

[2025-06-15 18:20:40.107] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20128} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:20:40.151] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20129} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15356221%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15356221, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:20:40.108', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Air Fresheners(空气清新剂)/Spray(喷雾)'
where id = 45
[2025-06-15 18:20:40.151] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 45
[2025-06-15 18:20:40.152] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=73 ms, AverageTime=73 ms

[2025-06-15 18:20:49.326] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:20:50.113] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20130} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:20:50.152] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20131} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17833726011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17833726011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:20:50.114', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Artificial Plants & Greenery'
where id = 46
[2025-06-15 18:20:50.152] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 46
[2025-06-15 18:20:50.153] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=63 ms, AverageTime=63 ms

[2025-06-15 18:21:00.104] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20132} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:21:00.148] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20133} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874217011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874217011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:21:00.105', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedding Accessories(床上用品配件)/Twin Bed Bridges(双床桥)'
where id = 47
[2025-06-15 18:21:00.148] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 47
[2025-06-15 18:21:00.149] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=65 ms, AverageTime=65 ms

[2025-06-15 18:21:10.121] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20134} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:21:10.145] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20135} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A1063242%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 1063242, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:21:10.121', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bath Rugs(浴毯)'
where id = 48
[2025-06-15 18:21:10.145] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 48
[2025-06-15 18:21:10.145] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:21:20.101] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20136} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:21:20.124] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20137} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433414011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433414011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:21:20.102', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Artwork(艺术品)/Mixed Media(混合介质)'
where id = 49
[2025-06-15 18:21:20.124] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 49
[2025-06-15 18:21:20.125] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:21:22.331] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:21:30.102] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20138} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:21:30.128] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20139} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433415011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433415011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:21:30.102', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Artwork(艺术品)/Paintings(绘画)'
where id = 50
[2025-06-15 18:21:30.128] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 50
[2025-06-15 18:21:30.129] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:21:40.313] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20140} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:21:40.341] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20141} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A85975011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 85975011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:21:40.314', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Brushes(刷子)/Toilet Brushes & Holders(马桶刷和支架)'
where id = 51
[2025-06-15 18:21:40.341] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 51
[2025-06-15 18:21:40.341] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:21:50.102] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20142} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:21:50.125] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20143} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14087361%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14087361, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:21:50.103', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Artificial Shrubs & Topiaries/Artificial Shrubs'
where id = 52
[2025-06-15 18:21:50.125] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 52
[2025-06-15 18:21:50.125] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:21:55.347] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:22:00.143] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20144} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:22:00.189] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20145} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671068011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671068011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:22:00.144', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedding Sets & Collections(床上用品套装和系列)/Daybed Sets(日床套装)'
where id = 53
[2025-06-15 18:22:00.190] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 53
[2025-06-15 18:22:00.190] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=73 ms, AverageTime=73 ms

[2025-06-15 18:22:10.099] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20146} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:22:10.131] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20147} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3731911%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3731911, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:22:10.100', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathroom Accessory Sets(浴室配件套装)'
where id = 54
[2025-06-15 18:22:10.131] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 54
[2025-06-15 18:22:10.132] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 18:22:20.099] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20148} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:22:20.127] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20149} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433416011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433416011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:22:20.100', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Artwork(艺术品)/Photographs(照片)'
where id = 55
[2025-06-15 18:22:20.127] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 55
[2025-06-15 18:22:20.127] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:22:28.354] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:22:30.149] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20150} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:22:30.176] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20151} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14087371%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14087371, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:22:30.151', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Artificial Shrubs & Topiaries/Artificial Topiaries'
where id = 56
[2025-06-15 18:22:30.176] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 56
[2025-06-15 18:22:30.177] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:22:40.125] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20152} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:22:40.156] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20153} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433417011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433417011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:22:40.127', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Artwork(艺术品)/Posters(海报)'
where id = 57
[2025-06-15 18:22:40.156] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 57
[2025-06-15 18:22:40.157] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:22:50.109] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20154} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:22:50.142] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20155} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2245498011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2245498011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:22:50.110', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Dusting(除尘)/Feather Dusters(羽毛掸子)'
where id = 58
[2025-06-15 18:22:50.143] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 58
[2025-06-15 18:22:50.143] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:23:00.098] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20156} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:23:00.128] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20157} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14087381%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14087381, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:23:00.100', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Artificial Trees'
where id = 59
[2025-06-15 18:23:00.128] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 59
[2025-06-15 18:23:00.129] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 18:23:01.366] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:23:10.103] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20158} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:23:10.136] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20159} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733641%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733641, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:23:10.105', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/End Tables(结束表)'
where id = 60
[2025-06-15 18:23:10.136] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 60
[2025-06-15 18:23:10.136] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 18:23:20.099] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20160} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:23:20.122] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20161} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671046011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671046011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:23:20.099', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedspreads, Coverlets & Sets(床罩、床罩和套装)/Bedspread & Coverlet Sets(床罩和床罩套装)'
where id = 61
[2025-06-15 18:23:20.122] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 61
[2025-06-15 18:23:20.122] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:23:30.102] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20162} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:23:30.126] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20163} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3785121%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3785121, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:23:30.102', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathroom Mirrors(浴室镜子)/Makeup Mirrors(化妆镜)'
where id = 62
[2025-06-15 18:23:30.126] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 62
[2025-06-15 18:23:30.127] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:23:34.378] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:23:40.103] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20164} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:23:40.131] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20165} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433418011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433418011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:23:40.104', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Artwork(艺术品)/Prints(打印)'
where id = 63
[2025-06-15 18:23:40.131] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 63
[2025-06-15 18:23:40.132] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:23:50.097] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20166} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:23:50.122] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20167} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14087401%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14087401, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:23:50.099', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Artificial Vegetables'
where id = 64
[2025-06-15 18:23:50.122] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 64
[2025-06-15 18:23:50.123] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 18:24:00.108] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20168} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:24:00.147] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20169} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733841%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733841, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:24:00.109', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Glass Display Cabinets(玻璃展示柜)'
where id = 65
[2025-06-15 18:24:00.148] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 65
[2025-06-15 18:24:00.148] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=63 ms, AverageTime=63 ms

[2025-06-15 18:24:07.398] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:24:10.110] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20170} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:24:10.145] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20171} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732161%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732161, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:24:10.112', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedspreads, Coverlets & Sets(床罩、床罩和套装)/Bedspreads & Coverlets(床罩和床罩)'
where id = 66
[2025-06-15 18:24:10.145] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 66
[2025-06-15 18:24:10.146] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=62 ms, AverageTime=62 ms

[2025-06-15 18:24:20.105] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20172} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:24:20.131] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20173} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3785131%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3785131, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:24:20.106', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathroom Mirrors(浴室镜子)/Shower Mirrors(淋浴镜)'
where id = 67
[2025-06-15 18:24:20.131] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 67
[2025-06-15 18:24:20.131] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:24:24.909] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20174} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:24:24.934] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20175} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433419011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433419011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:24:24.911', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Artwork(艺术品)/Wall Stickers(墙贴)'
where id = 68
[2025-06-15 18:24:24.934] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 68
[2025-06-15 18:24:24.934] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:24:34.913] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20176} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:24:34.940] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20177} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15356141%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15356141, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:24:34.914', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/All-Purpose Cleaners(通用清洁剂)'
where id = 69
[2025-06-15 18:24:34.940] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 69
[2025-06-15 18:24:34.941] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:24:40.410] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:24:44.929] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20178} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:24:44.952] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20179} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16183931011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16183931011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:24:44.930', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Decorative Swags'
where id = 70
[2025-06-15 18:24:44.954] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 70
[2025-06-15 18:24:44.954] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 18:24:46.061] [ERROR] [http-nio-32000-exec-6] [com.xinghuo.exception.exception.ResultException] [?] [?] - 系统异常:Request method 'POST' is not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:265)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:441)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:382)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:126)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:68)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:507)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1283)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1065)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 18:24:54.915] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20180} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:24:54.939] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20181} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733741%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733741, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:24:54.915', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Ladder Shelves(梯子搁板)'
where id = 71
[2025-06-15 18:24:54.939] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 71
[2025-06-15 18:24:54.939] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:24:59.709] [ERROR] [http-nio-32000-exec-8] [com.xinghuo.exception.exception.ResultException] [?] [?] - 系统异常:Request method 'POST' is not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:265)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:441)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:382)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:126)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:68)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:507)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1283)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1065)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 18:25:00.003] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:25:00.003] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:25:00.177] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20182} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 17:55:00.003')
[2025-06-15 18:25:00.178] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:25:00.178] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:25:00.286] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20183} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:25:00.178'
	and country = 'CN')
[2025-06-15 18:25:00.316] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20184} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:25:00.317] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:25:00.317] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30999 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:25:00.329] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20185} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:25:00.329] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:25:00.329] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:25:04.910] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20186} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:25:04.935] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20187} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17738953011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17738953011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:25:04.910', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathroom Mirrors(浴室镜子)/Wall-Mounted Vanity Mirrors(壁挂式化妆镜)'
where id = 72
[2025-06-15 18:25:04.936] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 72
[2025-06-15 18:25:04.936] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:25:13.426] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:25:14.929] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20188} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:25:14.960] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20189} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734221%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734221, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:25:14.929', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Nesting Tables(嵌套表)'
where id = 73
[2025-06-15 18:25:14.960] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 73
[2025-06-15 18:25:14.960] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:25:20.222] [ERROR] [http-nio-32000-exec-2] [com.xinghuo.exception.exception.ResultException] [?] [?] - 系统异常:Request method 'POST' is not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:265)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:441)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:382)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:126)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:68)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:507)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1283)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1065)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 18:25:24.930] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20190} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:25:24.960] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20191} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732181%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732181, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:25:24.931', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Blankets & Throws(毯子和投掷)/Bed Blankets(床毯)'
where id = 74
[2025-06-15 18:25:24.960] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 74
[2025-06-15 18:25:24.961] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=61 ms, AverageTime=61 ms

[2025-06-15 18:25:34.912] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20192} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:25:34.934] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20193} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524334011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524334011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:25:34.913', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Bathroom Cleaners(浴室清洁剂)/Lime & Rust Removers(石灰除锈剂)'
where id = 75
[2025-06-15 18:25:34.934] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 75
[2025-06-15 18:25:34.934] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:25:40.201] [ERROR] [http-nio-32000-exec-5] [com.xinghuo.exception.exception.ResultException] [?] [?] - 系统异常:Request method 'POST' is not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:265)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:441)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:382)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:126)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:68)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:507)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1283)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1065)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 18:25:44.932] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20194} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:25:44.956] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20195} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3254639011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3254639011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:25:44.932', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Ottomans(奥斯曼人)'
where id = 76
[2025-06-15 18:25:44.957] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 76
[2025-06-15 18:25:44.957] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=65 ms, AverageTime=65 ms

[2025-06-15 18:25:46.427] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:25:54.924] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20196} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:25:54.951] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20197} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A85961011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 85961011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:25:54.925', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathtub Accessories(浴缸配件)/Bathtub Appliques(浴缸贴花)'
where id = 77
[2025-06-15 18:25:54.951] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 77
[2025-06-15 18:25:54.952] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:26:04.935] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20198} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:26:04.959] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20199} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433428011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433428011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:26:04.935', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bath Linen(浴巾)/Bath Mats(浴垫)'
where id = 78
[2025-06-15 18:26:04.959] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 78
[2025-06-15 18:26:04.960] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:26:14.921] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20200} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:26:14.942] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20201} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734381%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734381, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:26:14.921', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Accessories/Accessory Sets'
where id = 79
[2025-06-15 18:26:14.942] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 79
[2025-06-15 18:26:14.942] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:26:19.428] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:26:24.926] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20202} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:26:24.952] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20203} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16353441%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16353441, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:26:24.926', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Quilt Stands(被子架)'
where id = 80
[2025-06-15 18:26:24.952] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 80
[2025-06-15 18:26:24.952] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:26:34.931] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20204} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:26:34.952] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20205} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A85963011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 85963011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:26:34.931', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathtub Accessories(浴缸配件)/Bathtub Mats(浴缸垫)'
where id = 81
[2025-06-15 18:26:34.952] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 81
[2025-06-15 18:26:34.952] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:26:44.912] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20206} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:26:44.938] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20207} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433422011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433422011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:26:44.912', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bath Linen(浴巾)/Bath Towels(浴巾)'
where id = 82
[2025-06-15 18:26:44.938] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 82
[2025-06-15 18:26:44.938] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:26:52.430] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:26:54.911] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20208} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:26:54.934] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20209} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A19294221011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 19294221011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:26:54.912', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Accessories/Candle Shades'
where id = 83
[2025-06-15 18:26:54.935] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 83
[2025-06-15 18:26:54.935] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:27:04.917] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20210} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:27:04.942] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20211} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734261%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734261, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:27:04.917', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Room Dividers(房间隔断)'
where id = 84
[2025-06-15 18:27:04.942] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 84
[2025-06-15 18:27:04.942] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:27:14.913] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20212} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:27:14.936] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20213} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524335011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524335011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:27:14.913', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Bathroom Cleaners(浴室清洁剂)/Mold & Mildew Removers(霉菌去除剂)'
where id = 85
[2025-06-15 18:27:14.937] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 85
[2025-06-15 18:27:14.937] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:27:24.928] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20214} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:27:24.951] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20215} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A85966011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 85966011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:27:24.928', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathtub Accessories(浴缸配件)/Bathtub Trays(浴缸托盘)'
where id = 86
[2025-06-15 18:27:24.951] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 86
[2025-06-15 18:27:24.952] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:27:25.432] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:27:34.922] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20216} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:27:34.947] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20217} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433426011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433426011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:27:34.922', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bath Linen(浴巾)/Hand Towels(手巾)'
where id = 87
[2025-06-15 18:27:34.947] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 87
[2025-06-15 18:27:34.947] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:27:44.910] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20218} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:27:44.942] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20219} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A19294222011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 19294222011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:27:44.911', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Accessories/Candle Sleeves'
where id = 88
[2025-06-15 18:27:44.942] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 88
[2025-06-15 18:27:44.942] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:27:54.927] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20220} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:27:54.951] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20221} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733651%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733651, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:27:54.927', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Sofa & Console Tables(沙发和控制台桌)'
where id = 89
[2025-06-15 18:27:54.951] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 89
[2025-06-15 18:27:54.951] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:27:58.434] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:28:04.924] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20222} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:28:04.946] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20223} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524333011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524333011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:28:04.925', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Bathroom Cleaners(浴室清洁剂)/Multipurpose Bathroom Cleaners(多功能浴室清洁剂)'
where id = 90
[2025-06-15 18:28:04.946] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 90
[2025-06-15 18:28:04.946] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:28:14.913] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20224} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:28:14.936] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20225} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A344741011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 344741011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:28:14.914', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathtub Accessories(浴缸配件)/Drain Stoppers(排水限位器)'
where id = 91
[2025-06-15 18:28:14.937] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 91
[2025-06-15 18:28:14.937] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:28:24.908] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20226} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:28:24.931] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20227} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433427011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433427011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:28:24.909', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bath Linen(浴巾)/Washcloths(毛巾)'
where id = 92
[2025-06-15 18:28:24.931] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 92
[2025-06-15 18:28:24.931] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:28:31.436] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:28:34.939] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20228} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:28:34.960] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20229} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14058581%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14058581, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:28:34.939', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Blankets & Throws(毯子和投掷)/Throws(投掷)'
where id = 93
[2025-06-15 18:28:34.961] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 93
[2025-06-15 18:28:34.961] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=40 ms, AverageTime=40 ms

[2025-06-15 18:28:44.910] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20230} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:28:44.934] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20231} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734341%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734341, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:28:44.911', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Accessories/Candlesnuffers'
where id = 94
[2025-06-15 18:28:44.934] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 94
[2025-06-15 18:28:44.934] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:28:54.932] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20232} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:28:54.961] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20233} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A681151011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 681151011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:28:54.932', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Storage Cabinets(储物柜)'
where id = 95
[2025-06-15 18:28:54.961] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 95
[2025-06-15 18:28:54.961] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 18:29:04.436] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:29:04.922] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20234} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:29:04.946] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20235} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524336011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524336011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:29:04.922', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Bathroom Cleaners(浴室清洁剂)/Soap Scum Removers(肥皂浮渣清除剂)'
where id = 96
[2025-06-15 18:29:04.946] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 96
[2025-06-15 18:29:04.946] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:29:14.907] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20236} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:29:14.927] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20237} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A672238011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 672238011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:29:14.907', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathtub Accessories(浴缸配件)/Splash Guards(防溅板)'
where id = 97
[2025-06-15 18:29:14.927] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 97
[2025-06-15 18:29:14.927] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=39 ms, AverageTime=39 ms

[2025-06-15 18:29:24.924] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20238} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:29:24.944] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20239} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874220011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874220011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:29:24.925', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Blankets & Throws(毯子和投掷)/Wearable Blankets(可穿戴毛毯)'
where id = 98
[2025-06-15 18:29:24.944] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 98
[2025-06-15 18:29:24.945] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:29:34.916] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20240} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:29:34.938] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20241} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734351%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734351, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:29:34.916', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Accessories/Replacement Wicks & Trimmers'
where id = 99
[2025-06-15 18:29:34.938] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 99
[2025-06-15 18:29:34.938] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=41 ms, AverageTime=41 ms

[2025-06-15 18:29:37.439] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:29:44.914] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20242} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:29:44.941] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20243} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524337011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524337011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:29:44.916', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Bathroom Cleaners(浴室清洁剂)/Toilet Cleaners(厕所清洁剂)'
where id = 100
[2025-06-15 18:29:44.942] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 100
[2025-06-15 18:29:44.942] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:29:54.985] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20244} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:29:55.007] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20245} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A6810213011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 6810213011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:29:54.985', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Fixed Showerheads(固定喷头)'
where id = 101
[2025-06-15 18:29:55.007] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 101
[2025-06-15 18:29:55.008] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=107 ms, AverageTime=107 ms

[2025-06-15 18:30:00.002] [INFO ] [JetCacheDefaultExecutor] [com.alicp.jetcache.support.StatInfoLogger] [?] [?] - jetcache stat from 2025-06-15 18:15:00,006 to 2025-06-15 18:30:00,001
cache           |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
xaceCache       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

[2025-06-15 18:30:00.004] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:30:00.004] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:30:00.136] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20246} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 18:00:00.004')
[2025-06-15 18:30:00.137] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:30:00.137] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:30:00.196] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20247} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:30:00.137'
	and country = 'CN')
[2025-06-15 18:30:03.209] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20248} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:30:03.209] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:30:03.209] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30982 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:30:03.266] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20249} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:30:03.266] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:30:03.266] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:30:03.267] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - 开始执行Fruugo监控数据定时刷新任务
[2025-06-15 18:30:03.267] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 开始生成 2025-06-15 的监控数据
[2025-06-15 18:30:03.285] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20250} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where (stat_date = '2025-06-15')
[2025-06-15 18:30:03.653] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20251} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_offer
[2025-06-15 18:30:03.805] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20252} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 0)
[2025-06-15 18:30:04.432] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20253} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 2)
[2025-06-15 18:30:04.927] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20255} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:30:04.954] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20256} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433431011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433431011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:30:04.928', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathroom Cups(浴室杯子)'
where id = 102
[2025-06-15 18:30:04.954] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 102
[2025-06-15 18:30:04.954] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 18:30:05.796] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20254} executed. select count(*) as total
from zz_fruugo_collect_offer
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:30:10.441] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:30:15.008] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20258} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:30:15.035] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20259} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874221011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874221011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:30:15.009', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Blankets & Throws(毯子和投掷)/Weighted Blankets(加重毛毯)'
where id = 103
[2025-06-15 18:30:15.035] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 103
[2025-06-15 18:30:15.036] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=146 ms, AverageTime=146 ms

[2025-06-15 18:30:24.912] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20260} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:30:24.941] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20261} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734371%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734371, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:30:24.913', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Accessories/Travel Tins'
where id = 104
[2025-06-15 18:30:24.941] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 104
[2025-06-15 18:30:24.941] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:30:28.842] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20257} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_sale
[2025-06-15 18:30:28.858] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20262} executed. select count(*) as total
from zz_fruugo_collect_sale
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:30:28.917] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20263} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_seller
[2025-06-15 18:30:28.941] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20264} executed. select count(*) as total
from zz_fruugo_seller
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:30:34.872] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20265} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product
[2025-06-15 18:30:34.920] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20267} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:30:34.951] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20268} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A684877011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 684877011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:30:34.921', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Storage Trunks & Chests(储物箱和箱子)/Storage Chests(储物箱)'
where id = 105
[2025-06-15 18:30:34.951] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 105
[2025-06-15 18:30:34.951] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:30:38.178] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20266} executed. select count(*) as total
from zz_fruugo_product
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:30:43.442] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:30:44.918] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20270} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:30:44.946] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20271} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A344739011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 344739011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:30:44.918', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Hand Dryers(干手器)'
where id = 106
[2025-06-15 18:30:44.946] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 106
[2025-06-15 18:30:44.946] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:30:54.915] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20272} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:30:54.936] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20273} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433430011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433430011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:30:54.915', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathroom Garbage Cans(浴室垃圾桶)'
where id = 107
[2025-06-15 18:30:54.936] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 107
[2025-06-15 18:30:54.936] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=37 ms, AverageTime=37 ms

[2025-06-15 18:31:04.913] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20274} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:31:04.936] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20275} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14163030011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14163030011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:31:04.913', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Carpet Cleaners(地毯清洁剂)/Carpet Machine Detergents(地毯机械洗涤剂)'
where id = 108
[2025-06-15 18:31:04.936] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 108
[2025-06-15 18:31:04.936] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:31:14.911] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20276} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:31:14.932] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20277} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734271%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734271, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:31:14.911', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Storage Trunks & Chests(储物箱和箱子)/Storage Trunks(储物行李箱)'
where id = 109
[2025-06-15 18:31:14.932] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 109
[2025-06-15 18:31:14.932] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=37 ms, AverageTime=37 ms

[2025-06-15 18:31:16.444] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:31:24.929] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20278} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:31:25.019] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20279} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A6810212011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 6810212011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:31:24.929', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Handheld Showers(手持淋浴)'
where id = 110
[2025-06-15 18:31:25.019] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 110
[2025-06-15 18:31:25.020] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=116 ms, AverageTime=116 ms

[2025-06-15 18:31:34.919] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20280} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:31:34.941] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20281} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433434011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433434011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:31:34.920', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Shower Curtains(浴帘)'
where id = 111
[2025-06-15 18:31:34.941] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 111
[2025-06-15 18:31:34.941] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 18:31:44.909] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20282} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:31:44.930] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20283} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14053321%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14053321, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:31:44.909', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Comforters & Sets(被子和套装)/Comforter Sets(被子套装)'
where id = 112
[2025-06-15 18:31:44.930] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 112
[2025-06-15 18:31:44.930] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:31:49.445] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:31:54.527] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20269} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product_sku
[2025-06-15 18:31:54.925] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20285} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:31:54.973] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20286} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734681%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734681, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:31:54.926', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Bowl Candleholders'
where id = 113
[2025-06-15 18:31:54.974] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 113
[2025-06-15 18:31:54.974] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=86 ms, AverageTime=86 ms

[2025-06-15 18:32:04.917] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20287} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:32:04.942] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20288} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14163029011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14163029011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:32:04.917', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Carpet Cleaners(地毯清洁剂)/Carpet Spot Cleaning Sprays(地毯斑点清洁喷雾剂)'
where id = 114
[2025-06-15 18:32:04.942] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 114
[2025-06-15 18:32:04.942] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:32:14.926] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20289} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:32:14.951] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20290} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433435011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433435011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:32:14.927', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Towel Racks(毛巾架)'
where id = 115
[2025-06-15 18:32:14.951] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 115
[2025-06-15 18:32:14.951] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:32:22.457] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:32:24.923] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20291} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:32:24.954] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20292} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A1199128%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 1199128, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:32:24.924', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Comforters & Sets(被子和套装)/Comforters(被子)'
where id = 116
[2025-06-15 18:32:24.955] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 116
[2025-06-15 18:32:24.955] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:32:34.922] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20293} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:32:34.945] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20294} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734631%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734631, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:32:34.923', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Candelabras'
where id = 117
[2025-06-15 18:32:34.945] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 117
[2025-06-15 18:32:34.945] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=38 ms, AverageTime=38 ms

[2025-06-15 18:32:44.906] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20295} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:32:44.938] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20296} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749741%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749741, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:32:44.907', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Bathroom Trays(浴室托盘)'
where id = 118
[2025-06-15 18:32:44.938] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 118
[2025-06-15 18:32:44.938] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:32:54.919] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20297} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:32:54.942] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20298} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3743601%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3743601, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:32:54.919', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Carpet Cleaners(地毯清洁剂)/Carpet Stain Precleaners(地毯污渍预清洁)'
where id = 119
[2025-06-15 18:32:54.942] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 119
[2025-06-15 18:32:54.942] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:32:55.464] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:33:04.003] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20284} executed. select count(*) as total
from zz_fruugo_product_sku
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:33:04.022] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20299} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where id = '703721994465583173'
[2025-06-15 18:33:04.047] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20300} executed. update zz_fruugo_monitor
set stat_date = TIMESTAMP '2025-06-15 00:00:00.000', collect_offer_total = 581232, collect_offer_pending = 120267, collect_offer_processed = 371647, collect_offer_new = 0, collect_sale_total = 8180610, collect_sale_new = 0, seller_total = 2960, seller_new = 0, product_total = 1032438, product_new = 0, sku_total = 2684538, sku_new = 0, f_created_at = TIMESTAMP '2025-06-15 00:00:00.000', f_last_updated_at = TIMESTAMP '2025-06-15 18:33:04.003'
where id = '703721994465583173'
[2025-06-15 18:33:04.047] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 生成 2025-06-15 的监控数据 成功
[2025-06-15 18:33:04.047] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo基础监控数据刷新 成功
[2025-06-15 18:33:04.047] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成今日的Fruugo数据监控信息
[2025-06-15 18:33:04.047] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成指定日期的Fruugo数据监控信息，日期: 2025-06-15
[2025-06-15 18:33:04.047] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 开始生成 2025-06-15 的监控数据
[2025-06-15 18:33:04.058] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20301} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where (stat_date = '2025-06-15')
[2025-06-15 18:33:04.930] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20303} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:33:04.971] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20304} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734621%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734621, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:33:04.930', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Candle Chandeliers'
where id = 120
[2025-06-15 18:33:04.971] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 120
[2025-06-15 18:33:04.971] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=76 ms, AverageTime=76 ms

[2025-06-15 18:33:06.572] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20302} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_offer
[2025-06-15 18:33:06.745] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20305} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 0)
[2025-06-15 18:33:07.309] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20306} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 2)
[2025-06-15 18:33:09.763] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20307} executed. select count(*) as total
from zz_fruugo_collect_offer
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:33:14.915] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20309} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:33:14.982] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20310} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3743581%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3743581, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:33:14.916', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Carpet Deodorizers(地毯除臭剂)'
where id = 121
[2025-06-15 18:33:14.982] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 121
[2025-06-15 18:33:14.982] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=94 ms, AverageTime=94 ms

[2025-06-15 18:33:24.922] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20311} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:33:24.949] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20312} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3785121%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3785121, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:33:24.923', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Bathroom Mirrors(浴室镜子)/Makeup Mirrors(化妆镜)'
where id = 122
[2025-06-15 18:33:24.949] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 122
[2025-06-15 18:33:24.949] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:33:28.475] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:33:34.911] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20313} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:33:34.935] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20314} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749751%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749751, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:33:34.911', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Canisters(罐)'
where id = 123
[2025-06-15 18:33:34.935] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 123
[2025-06-15 18:33:34.935] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:33:38.335] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20308} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_sale
[2025-06-15 18:33:38.361] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20315} executed. select count(*) as total
from zz_fruugo_collect_sale
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:33:38.574] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20316} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_seller
[2025-06-15 18:33:38.596] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20317} executed. select count(*) as total
from zz_fruugo_seller
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:33:44.915] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20319} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:33:44.984] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20320} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433432011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433432011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:33:44.915', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Fixtures(浴室固定装置)/Medicine Cabinets(药柜)'
where id = 124
[2025-06-15 18:33:44.985] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 124
[2025-06-15 18:33:44.985] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=91 ms, AverageTime=91 ms

[2025-06-15 18:33:49.287] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20318} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product
[2025-06-15 18:33:54.927] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20322} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:33:54.966] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20323} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671060011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671060011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:33:54.928', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Decorative Pillows, Inserts & Covers(装饰枕头、插件和盖子)/Floor Pillows & Cushions(地板枕头和靠垫)'
where id = 125
[2025-06-15 18:33:54.967] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 125
[2025-06-15 18:33:54.967] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=72 ms, AverageTime=72 ms

[2025-06-15 18:33:55.350] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20321} executed. select count(*) as total
from zz_fruugo_product
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:34:01.492] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:34:04.922] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20325} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:34:04.951] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20326} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734711%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734711, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:34:04.922', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Candle Lamps'
where id = 126
[2025-06-15 18:34:04.951] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 126
[2025-06-15 18:34:04.951] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:34:14.924] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20327} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:34:14.964] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20328} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3785131%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3785131, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:34:14.928', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Bathroom Mirrors(浴室镜子)/Shower Mirrors(淋浴镜)'
where id = 127
[2025-06-15 18:34:14.964] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 127
[2025-06-15 18:34:14.965] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=64 ms, AverageTime=64 ms

[2025-06-15 18:34:24.915] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20329} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:34:24.938] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20330} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749771%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749771, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:34:24.915', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Cup Holders(杯架)'
where id = 128
[2025-06-15 18:34:24.939] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 128
[2025-06-15 18:34:24.939] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:34:34.511] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:34:34.916] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20331} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:34:34.946] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20332} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433433011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433433011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:34:34.918', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Fixtures(浴室固定装置)/Shelves(货架)'
where id = 129
[2025-06-15 18:34:34.946] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 129
[2025-06-15 18:34:34.946] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:34:44.922] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20333} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:34:44.952] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20334} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732331%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732331, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:34:44.922', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Decorative Pillows, Inserts & Covers(装饰枕头、插件和盖子)/Pillow Inserts(枕头插件)'
where id = 130
[2025-06-15 18:34:44.952] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 130
[2025-06-15 18:34:44.952] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:34:54.919] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20335} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:34:54.942] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20336} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734691%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734691, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:34:54.920', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Candle Sconces'
where id = 131
[2025-06-15 18:34:54.942] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 131
[2025-06-15 18:34:54.944] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 18:35:04.932] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20337} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:35:04.978] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20338} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17738953011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17738953011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:35:04.932', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Bathroom Mirrors(浴室镜子)/Wall-Mounted Vanity Mirrors(壁挂式化妆镜)'
where id = 132
[2025-06-15 18:35:04.978] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 132
[2025-06-15 18:35:04.978] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=80 ms, AverageTime=80 ms

[2025-06-15 18:35:07.527] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:35:14.952] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20339} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:35:14.996] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20340} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433471011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433471011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:35:14.953', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Fixtures(浴室固定装置)/Vanities(虚荣)'
where id = 133
[2025-06-15 18:35:14.996] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 133
[2025-06-15 18:35:14.996] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=93 ms, AverageTime=93 ms

[2025-06-15 18:35:16.599] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20324} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product_sku
[2025-06-15 18:35:24.925] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20342} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:35:24.948] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20343} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16183925011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16183925011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:35:24.926', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Decorative Pillows, Inserts & Covers(装饰枕头、插件和盖子)/Poufs(磅)'
where id = 134
[2025-06-15 18:35:24.948] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 134
[2025-06-15 18:35:24.948] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:35:34.910] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20344} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:35:35.121] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20345} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734291%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734291, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:35:34.911', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Candleholder Sets'
where id = 135
[2025-06-15 18:35:35.122] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 135
[2025-06-15 18:35:35.122] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=233 ms, AverageTime=233 ms

[2025-06-15 18:35:40.536] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:35:44.917] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20346} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:35:44.939] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20347} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A85975011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 85975011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:35:44.917', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Brushes(刷子)/Toilet Brushes & Holders(马桶刷和支架)'
where id = 136
[2025-06-15 18:35:44.939] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 136
[2025-06-15 18:35:44.939] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=40 ms, AverageTime=40 ms

[2025-06-15 18:35:54.911] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20348} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:35:54.992] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20349} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732921%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732921, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:35:54.911', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Bathroom Sets(浴室套装)'
where id = 137
[2025-06-15 18:35:54.992] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 137
[2025-06-15 18:35:54.992] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=101 ms, AverageTime=101 ms

[2025-06-15 18:36:04.916] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20350} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:36:04.940] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20351} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749761%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749761, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:36:04.917', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Dispensers(分配器)/Countertop Soap Dispensers(台面肥皂分配器)'
where id = 138
[2025-06-15 18:36:04.940] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 138
[2025-06-15 18:36:04.940] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 18:36:13.559] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:36:14.933] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20352} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:36:14.958] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20353} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732341%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732341, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:36:14.935', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Decorative Pillows, Inserts & Covers(装饰枕头、插件和盖子)/Throw Pillow Covers(扔枕头套)'
where id = 139
[2025-06-15 18:36:14.958] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 139
[2025-06-15 18:36:14.958] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:36:24.957] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20354} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:36:24.983] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20355} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734611%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734611, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:36:24.958', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Candlestick Holders'
where id = 140
[2025-06-15 18:36:24.983] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 140
[2025-06-15 18:36:24.984] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=86 ms, AverageTime=86 ms

[2025-06-15 18:36:34.929] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20356} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:36:34.965] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20357} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A344744011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 344744011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:36:34.930', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Dispensers(分配器)/Lotion Dispensers(乳液分配器)'
where id = 141
[2025-06-15 18:36:34.965] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 141
[2025-06-15 18:36:34.965] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=75 ms, AverageTime=75 ms

[2025-06-15 18:36:44.919] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20358} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:36:44.948] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20359} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A335116011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 335116011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:36:44.919', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Bathroom Shelves(浴室搁板)'
where id = 142
[2025-06-15 18:36:44.948] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 142
[2025-06-15 18:36:44.949] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 18:36:46.576] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:36:54.918] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20360} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:36:54.946] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20361} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524361011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524361011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:36:54.918', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Cleaning Cloths(清洁布)'
where id = 143
[2025-06-15 18:36:54.946] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 143
[2025-06-15 18:36:54.946] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:37:04.914] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20362} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:37:04.940] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20363} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433449011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433449011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:37:04.915', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows(枕头)'
where id = 144
[2025-06-15 18:37:04.941] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 144
[2025-06-15 18:37:04.941] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:37:14.546] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20341} executed. select count(*) as total
from zz_fruugo_product_sku
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:37:14.566] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20364} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where id = '703721994465583173'
[2025-06-15 18:37:14.603] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20365} executed. update zz_fruugo_monitor
set stat_date = TIMESTAMP '2025-06-15 00:00:00.000', collect_offer_total = 581232, collect_offer_pending = 120267, collect_offer_processed = 371647, collect_offer_new = 0, collect_sale_total = 8180610, collect_sale_new = 0, seller_total = 2960, seller_new = 0, product_total = 1032438, product_new = 0, sku_total = 2684538, sku_new = 0, f_created_at = TIMESTAMP '2025-06-15 00:00:00.000', f_last_updated_at = TIMESTAMP '2025-06-15 18:37:14.547'
where id = '703721994465583173'
[2025-06-15 18:37:14.605] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 生成 2025-06-15 的监控数据 成功
[2025-06-15 18:37:14.605] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成指定日期的Fruugo数据监控信息成功，日期: 2025-06-15
[2025-06-15 18:37:14.605] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo数据监控信息刷新 成功
[2025-06-15 18:37:14.605] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo监控数据定时刷新任务执行完成
[2025-06-15 18:37:14.605] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:37:14.605] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:37:14.921] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20367} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:37:14.943] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20368} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732321%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732321, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:37:14.921', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Decorative Pillows, Inserts & Covers(装饰枕头、插件和盖子)/Throw Pillows(扔枕头)'
where id = 145
[2025-06-15 18:37:14.943] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 145
[2025-06-15 18:37:14.944] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:37:15.035] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20366} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 18:07:14.605')
[2025-06-15 18:37:15.036] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:37:15.036] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:37:15.074] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20369} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:37:15.036'
	and country = 'CN')
[2025-06-15 18:37:17.618] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20370} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:37:17.619] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:37:17.619] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30986 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:37:17.630] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20371} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:37:17.631] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:37:17.631] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:37:19.589] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:37:24.933] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20372} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:37:24.964] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20373} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734671%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734671, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:37:24.934', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Decorative Candle Lanterns'
where id = 146
[2025-06-15 18:37:24.965] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 146
[2025-06-15 18:37:24.965] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:37:34.927] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20374} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:37:34.954] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20375} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749791%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749791, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:37:34.927', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Dispensers(分配器)/Shower Dispensers(淋浴分配器)'
where id = 147
[2025-06-15 18:37:34.954] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 147
[2025-06-15 18:37:34.955] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:37:44.922] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20376} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:37:44.945] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20377} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16187581011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16187581011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:37:44.922', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Over-the-Toilet Storage(厕所储物)'
where id = 148
[2025-06-15 18:37:44.945] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 148
[2025-06-15 18:37:44.945] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 18:37:52.606] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:37:54.930] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20378} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:37:54.957] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20379} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433438011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433438011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:37:54.931', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedspreads & Coverlets(床罩和床罩)'
where id = 149
[2025-06-15 18:37:54.957] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 149
[2025-06-15 18:37:54.958] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:38:04.919] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20380} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:38:04.945] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20381} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734731%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734731, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:38:04.919', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Hurricane Candleholders'
where id = 150
[2025-06-15 18:38:04.946] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 150
[2025-06-15 18:38:04.946] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 18:38:14.908] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20382} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:38:14.978] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20383} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749801%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749801, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:38:14.908', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Soap Dishes(肥皂碟)'
where id = 151
[2025-06-15 18:38:14.986] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 151
[2025-06-15 18:38:14.987] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=99 ms, AverageTime=99 ms

[2025-06-15 18:38:24.938] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20384} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:38:24.970] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20385} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2245498011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2245498011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:38:24.940', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Dusting(除尘)/Feather Dusters(羽毛掸子)'
where id = 152
[2025-06-15 18:38:24.970] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 152
[2025-06-15 18:38:24.971] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=64 ms, AverageTime=64 ms

[2025-06-15 18:38:25.616] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:38:34.920] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20386} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:38:34.955] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20387} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14053331%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14053331, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:38:34.922', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvet Covers & Sets(羽绒被套和套装)/Duvet Cover Sets(羽绒被套)'
where id = 153
[2025-06-15 18:38:34.955] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 153
[2025-06-15 18:38:34.955] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:38:45.088] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20388} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:38:45.113] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20389} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16961058011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16961058011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:38:45.089', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Menorahs'
where id = 154
[2025-06-15 18:38:45.113] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 154
[2025-06-15 18:38:45.114] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:38:54.917] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20390} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:38:54.945] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20391} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749811%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749811, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:38:54.918', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Tissue Holders(组织支架)'
where id = 155
[2025-06-15 18:38:54.945] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 155
[2025-06-15 18:38:54.946] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:38:58.635] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:39:04.911] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20392} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:39:04.935] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20393} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15342901%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15342901, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:39:04.911', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Gloves(手套)'
where id = 156
[2025-06-15 18:39:04.935] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 156
[2025-06-15 18:39:04.935] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:39:14.921] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20394} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:39:14.956] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20395} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A335107011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 335107011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:39:14.922', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Towel Holders(毛巾架)/Towel Bars(毛巾杆)'
where id = 157
[2025-06-15 18:39:14.956] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 157
[2025-06-15 18:39:14.957] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=61 ms, AverageTime=61 ms

[2025-06-15 18:39:20.047] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20396} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:39:20.076] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20397} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433443011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433443011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:39:20.049', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Blankets & Quilts(毯子和被子)/Quilts(被子)'
where id = 158
[2025-06-15 18:39:20.076] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 158
[2025-06-15 18:39:20.077] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:39:30.037] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20398} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:39:30.063] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20399} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A1199134%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 1199134, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:39:30.038', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvet Covers & Sets(羽绒被套和套装)/Duvet Covers(羽绒被盖)'
where id = 159
[2025-06-15 18:39:30.063] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 159
[2025-06-15 18:39:30.063] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:39:31.656] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:39:33.015] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20400} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where id = 'submitForm'
[2025-06-15 18:39:33.016] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:

[2025-06-15 18:39:33.275] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20401} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where id = 'submitForm'
[2025-06-15 18:39:33.276] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:

[2025-06-15 18:39:40.044] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20402} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:39:40.068] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20403} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734651%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734651, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:39:40.045', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Tea Light Holders'
where id = 160
[2025-06-15 18:39:40.068] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 160
[2025-06-15 18:39:40.068] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 18:39:48.052] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20404} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where id = 'submitForm'
[2025-06-15 18:39:48.053] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:

[2025-06-15 18:39:50.050] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20405} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:39:50.075] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20406} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16350361%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16350361, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:39:50.051', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Toilet Paper Holders(卫生纸架)'
where id = 161
[2025-06-15 18:39:50.075] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 161
[2025-06-15 18:39:50.075] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:40:00.003] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:40:00.004] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:40:00.061] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20408} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:40:00.139] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20409} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16350721%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16350721, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:40:00.061', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Towel Holders(毛巾架)/Towel Racks(毛巾架)'
where id = 162
[2025-06-15 18:40:00.139] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 162
[2025-06-15 18:40:00.140] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=105 ms, AverageTime=105 ms

[2025-06-15 18:40:00.168] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20407} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 18:10:00.004')
[2025-06-15 18:40:00.168] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:40:00.168] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:40:00.236] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20410} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:40:00.168'
	and country = 'CN')
[2025-06-15 18:40:01.301] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20411} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:40:01.301] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:40:01.301] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [产品数据堆积] [产品数据]: 失败
[2025-06-15 18:40:01.396] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20412} executed. insert into zz_fruugo_alert_log (id, alert_type, alert_module, alert_content, alert_time
	, status, notify_type, ip, created_at, updated_at)
values ('704003857138628293', 5, 'product', '产品数据堆积严重，当前有 31012 条待处理产品数据，超过阈值(10000)，请检查产品处理系统', TIMESTAMP '2025-06-15 18:40:01.301'
	, 0, 1, '***********', TIMESTAMP '2025-06-15 18:40:01.301', TIMESTAMP '2025-06-15 18:40:01.301')
[2025-06-15 18:40:01.396] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31012 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:40:01.422] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20413} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:40:01.424] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:40:01.424] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:40:04.683] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:40:10.050] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20414} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:40:10.092] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20415} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433444011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433444011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:40:10.050', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Blankets & Quilts(毯子和被子)/Throw Blankets(扔毯子)'
where id = 163
[2025-06-15 18:40:10.092] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 163
[2025-06-15 18:40:10.092] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=63 ms, AverageTime=63 ms

[2025-06-15 18:40:20.056] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20416} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:40:20.085] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20417} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671048011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671048011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:40:20.056', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvets & Down Comforters(羽绒被和羽绒被)'
where id = 164
[2025-06-15 18:40:20.085] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 164
[2025-06-15 18:40:20.086] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:40:30.061] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20418} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:40:30.092] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20419} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A695476011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 695476011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:40:30.062', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Toilet Paper Storage Containers(卫生纸存储容器)'
where id = 165
[2025-06-15 18:40:30.092] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 165
[2025-06-15 18:40:30.094] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=58 ms, AverageTime=58 ms

[2025-06-15 18:40:37.696] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:40:40.044] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20420} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:40:40.070] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20421} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A335111011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 335111011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:40:40.044', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Towel Holders(毛巾架)/Towel Rings(毛巾环)'
where id = 166
[2025-06-15 18:40:40.070] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 166
[2025-06-15 18:40:40.071] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 18:40:50.055] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20422} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:40:50.078] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20423} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433445011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433445011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:40:50.056', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Blankets & Quilts(毯子和被子)/Wearable Blankets(可穿戴毛毯)'
where id = 167
[2025-06-15 18:40:50.079] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 167
[2025-06-15 18:40:50.079] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:40:54.240] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
[2025-06-15 18:40:54.323] [INFO ] [SpringApplicationShutdownHook] [o.s.scheduling.quartz.SchedulerFactoryBean] [?] [?] - Shutting down Quartz Scheduler
[2025-06-15 18:40:54.323] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
[2025-06-15 18:40:54.323] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
[2025-06-15 18:40:54.323] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
[2025-06-15 18:40:54.327] [INFO ] [SpringApplicationShutdownHook] [o.dromara.x.file.storage.core.FileStorageService] [?] [?] - 销毁存储平台 local-plus-1 成功
[2025-06-15 18:40:54.327] [INFO ] [Thread-12] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server stop.
[2025-06-15 18:40:57.333] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:40:57.333] [INFO ] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.thread.ExecutorRegistryThread] [?] [?] - >>>>>>>>>>> xxl-job registry-remove fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='xxl-job-executor-sample1', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connect timed out), for url : http://101.33.227.194:30020/xxl-job-admin/api/registryRemove, content=null]
[2025-06-15 18:40:57.333] [INFO ] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.thread.ExecutorRegistryThread] [?] [?] - >>>>>>>>>>> xxl-job, executor registry thread destroy.
[2025-06-15 18:40:57.333] [INFO ] [SpringApplicationShutdownHook] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server destroy success.
[2025-06-15 18:40:57.334] [INFO ] [xxl-job, executor JobLogFileCleanThread] [com.xxl.job.core.thread.JobLogFileCleanThread] [?] [?] - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
[2025-06-15 18:40:57.334] [INFO ] [xxl-job, executor TriggerCallbackThread] [com.xxl.job.core.thread.TriggerCallbackThread] [?] [?] - >>>>>>>>>>> xxl-job, executor callback thread destroy.
[2025-06-15 18:40:57.334] [INFO ] [Thread-11] [com.xxl.job.core.thread.TriggerCallbackThread] [?] [?] - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
[2025-06-15 18:40:57.337] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 18:40:59.351] [INFO ] [SpringApplicationShutdownHook] [com.alicp.jetcache.support.DefaultMetricsManager] [?] [?] - cache stat canceled
[2025-06-15 18:40:59.383] [INFO ] [SpringApplicationShutdownHook] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource start closing ....
[2025-06-15 18:40:59.385] [INFO ] [SpringApplicationShutdownHook] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1} closing ...
[2025-06-15 18:40:59.387] [INFO ] [SpringApplicationShutdownHook] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1} closed
[2025-06-15 18:40:59.387] [INFO ] [SpringApplicationShutdownHook] [c.b.d.d.destroyer.DefaultDataSourceDestroyer] [?] [?] - dynamic-datasource close the datasource named [master] success,
[2025-06-15 18:40:59.387] [INFO ] [SpringApplicationShutdownHook] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource all closed success,bye
[2025-06-15 18:41:01.552] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Starting XhAdminApplication using Java 20.0.1 with PID 18004 (G:\v2\51erp\51erp-service\xace-java-boot\xh-admin\target\classes started by Administrator in G:\v2\51erp)
[2025-06-15 18:41:01.552] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - The following 1 profile is active: "vpn"
[2025-06-15 18:41:03.913] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Multiple Spring Data modules found, entering strict repository configuration mode
[2025-06-15 18:41:03.914] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-06-15 18:41:03.936] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[2025-06-15 18:41:04.789] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.801] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.808] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.891] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.910] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.926] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.930] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.933] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.936] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.948] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.950] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.952] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.954] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.957] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.959] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.962] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.963] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.974] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.993] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.994] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:05.411] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat initialized with port 32000 (http)
[2025-06-15 18:41:05.422] [INFO ] [main] [org.apache.catalina.core.StandardService] [?] [?] - Starting service [Tomcat]
[2025-06-15 18:41:05.422] [INFO ] [main] [org.apache.catalina.core.StandardEngine] [?] [?] - Starting Servlet engine: [Apache Tomcat/10.1.19]
[2025-06-15 18:41:05.499] [INFO ] [main] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring embedded WebApplicationContext
[2025-06-15 18:41:05.500] [INFO ] [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] [?] [?] - Root WebApplicationContext: initialization completed in 3756 ms
[2025-06-15 18:41:05.967] [INFO ] [main] [org.redisson.Version] [?] [?] - Redisson 3.27.2
[2025-06-15 18:41:06.314] [INFO ] [redisson-netty-1-4] [org.redisson.connection.ConnectionsHolder] [?] [?] - 1 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 18:41:06.382] [INFO ] [redisson-netty-1-19] [org.redisson.connection.ConnectionsHolder] [?] [?] - 24 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 18:41:07.328] [INFO ] [main] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1,master} inited
[2025-06-15 18:41:07.329] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource - add a datasource named [master] success
[2025-06-15 18:41:07.329] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
[2025-06-15 18:41:08.569] [ERROR] [main] [c.baomidou.mybatisplus.core.MybatisConfiguration] [?] [?] - mapper[com.xinghuo.allegro.push.dao.PushOfferMapper.pushList1] is ignored, because it exists, maybe from xml file
[2025-06-15 18:41:08.872] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 18:41:08.873] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 18:41:08.873] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 18:41:08.875] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 18:41:08.879] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 18:41:36.991] [INFO ] [main] [com.xinghuo.fruugo.collect.util.FruugoSkuInfoUtil] [?] [?] - 注入FruugoImageService成功
[2025-06-15 18:41:36.992] [INFO ] [main] [com.xinghuo.fruugo.collect.util.FruugoSkuInfoUtil] [?] [?] - 设置allgeroUrl: http://127.0.0.1:3100/staticImage/
[2025-06-15 18:41:44.070] [INFO ] [main] [c.a.jetcache.autoconfigure.AbstractCacheAutoInit] [?] [?] - init cache area default , type= linkedhashmap
[2025-06-15 18:41:44.077] [INFO ] [main] [c.a.jetcache.autoconfigure.AbstractCacheAutoInit] [?] [?] - init cache area default , type= redis.springdata
[2025-06-15 18:41:44.099] [INFO ] [main] [com.alicp.jetcache.support.DefaultMetricsManager] [?] [?] - cache stat period at 15 MINUTES
[2025-06-15 18:41:44.126] [INFO ] [main] [c.a.j.redis.springdata.SpringDataBroadcastManager] [?] [?] - create RedisMessageListenerContainer instance
[2025-06-15 18:41:44.151] [INFO ] [main] [c.a.j.redis.springdata.SpringDataBroadcastManager] [?] [?] - subscribe jetcache invalidate notification. channel=projectA
[2025-06-15 18:41:52.758] [INFO ] [main] [c.xinghuo.common.database.config.IdGeneratorConfig] [?] [?] - 当前ID生成器编号: 732
[2025-06-15 18:41:53.503] [INFO ] [main] [com.xinghuo.scheduletask.config.XxlJobConfig] [?] [?] - >>>>>>>>>>> xxl-job config init.
[2025-06-15 18:41:53.555] [INFO ] [main] [o.d.x.file.storage.core.FileStorageServiceBuilder] [?] [?] - 加载本地升级版存储平台：local-plus-1
[2025-06-15 18:41:54.922] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Using default implementation for ThreadExecutor
[2025-06-15 18:41:54.932] [INFO ] [main] [org.quartz.core.SchedulerSignalerImpl] [?] [?] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[2025-06-15 18:41:54.932] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Quartz Scheduler v.2.3.2 created.
[2025-06-15 18:41:54.933] [INFO ] [main] [org.quartz.simpl.RAMJobStore] [?] [?] - RAMJobStore initialized.
[2025-06-15 18:41:54.934] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[2025-06-15 18:41:54.934] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
[2025-06-15 18:41:54.934] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Quartz scheduler version: 2.3.2
[2025-06-15 18:41:54.934] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6bda77da
[2025-06-15 18:41:55.814] [INFO ] [main] [o.s.b.actuate.endpoint.web.EndpointLinksResolver] [?] [?] - Exposing 14 endpoint(s) beneath base path '/actuator'
[2025-06-15 18:41:57.114] [INFO ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job register jobhandler success, name:defaultHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6f3b5684[class com.xinghuo.scheduletask.task.ScheduleTaskHandler#defaultHandler]
[2025-06-15 18:41:57.582] [INFO ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job registry-remove success, registryParam:{defaultHandler=com.xxl.job.core.handler.impl.MethodJobHandler@6f3b5684[class com.xinghuo.scheduletask.task.ScheduleTaskHandler#defaultHandler]}, registryResult:ReturnT [code=200, msg=null, content=null]
[2025-06-15 18:41:57.583] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 18:41:57.629] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat started on port 32000 (http) with context path ''
[2025-06-15 18:41:57.630] [INFO ] [main] [o.s.scheduling.quartz.SchedulerFactoryBean] [?] [?] - Starting Quartz Scheduler now
[2025-06-15 18:41:57.631] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
[2025-06-15 18:41:57.650] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Started XhAdminApplication in 56.616 seconds (process running for 57.316)
[2025-06-15 18:41:57.737] [INFO ] [Thread-12] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
[2025-06-15 18:41:58.116] [INFO ] [RMI TCP Connection(3)-**************] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-06-15 18:41:58.116] [INFO ] [RMI TCP Connection(3)-**************] [org.springframework.web.servlet.DispatcherServlet] [?] [?] - Initializing Servlet 'dispatcherServlet'
[2025-06-15 18:41:58.120] [INFO ] [RMI TCP Connection(3)-**************] [org.springframework.web.servlet.DispatcherServlet] [?] [?] - Completed initialization in 4 ms
[2025-06-15 18:42:00.464] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20000} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:42:00.511] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20001} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16250371%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16250371, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:42:00.471', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Aromatherapy Candles'
where id = 168
[2025-06-15 18:42:00.511] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 168
[2025-06-15 18:42:00.528] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=352 ms, AverageTime=352 ms

[2025-06-15 18:42:00.745] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:42:10.057] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20002} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:42:10.082] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20003} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749821%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749821, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:42:10.057', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Toothbrush Holders(牙刷架)'
where id = 169
[2025-06-15 18:42:10.082] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 169
[2025-06-15 18:42:10.082] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:42:20.063] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20004} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:42:20.097] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20005} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14253971%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14253971, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:42:20.065', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Mopping(拖地)/Accessories(附件)/Mop Handles(拖把手柄)'
where id = 170
[2025-06-15 18:42:20.097] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 170
[2025-06-15 18:42:20.097] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=59 ms, AverageTime=59 ms

[2025-06-15 18:42:30.084] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20006} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:42:30.115] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20007} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734301%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734301, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:42:30.085', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Candle Sets'
where id = 171
[2025-06-15 18:42:30.116] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 171
[2025-06-15 18:42:30.116] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 18:42:30.453] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20008} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:42:30.482] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20009} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A695492011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 695492011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:42:30.455', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Mopping(拖地)/Accessories(附件)/Refill Sponges(重新填充海绵)'
where id = 172
[2025-06-15 18:42:30.483] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 172
[2025-06-15 18:42:30.483] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 18:42:33.762] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:42:40.448] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20010} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:42:40.475] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20011} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A344745011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 344745011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:42:40.449', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Tumblers(不倒翁)'
where id = 173
[2025-06-15 18:42:40.476] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 173
[2025-06-15 18:42:40.476] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

