package com.xinghuo.amazon.collect.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xinghuo.amazon.collect.entity.AmazonPageTaskEntity;
import com.xinghuo.amazon.util.AmazonConstant;
import com.xinghuo.common.util.core.StrXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Amazon数据解析服务
 * 参考Python爬虫代码实现Amazon页面数据解析逻辑
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
public class AmazonDataParseService {

    /**
     * 解析Amazon列表页JSON数据，提取商品信息并创建页面任务
     *
     * @param jsonData 前端提交的JSON数据
     * @param listTaskId 列表任务ID
     * @return 解析出的页面任务列表
     */
    public List<AmazonPageTaskEntity> parseListPageData(String jsonData, Integer listTaskId) {
        List<AmazonPageTaskEntity> taskList = new ArrayList<>();
        
        if (StrXhUtil.isBlank(jsonData)) {
            log.warn("Amazon列表页JSON数据为空");
            return taskList;
        }
        
        try {
            JSONObject jsonObject = JSONObject.parseObject(jsonData);
            JSONArray products = jsonObject.getJSONArray("products");
            
            if (products == null || products.isEmpty()) {
                log.warn("Amazon列表页JSON数据中没有找到products数组");
                return taskList;
            }
            
            Date now = new Date();
            
            for (int i = 0; i < products.size(); i++) {
                JSONObject product = products.getJSONObject(i);
                AmazonPageTaskEntity task = parseProductData(product, listTaskId, now);
                if (task != null) {
                    taskList.add(task);
                }
            }
            
            log.info("成功解析Amazon列表页数据，共解析出{}个商品", taskList.size());
            
        } catch (Exception e) {
            log.error("解析Amazon列表页JSON数据失败", e);
        }
        
        return taskList;
    }

    /**
     * 解析单个商品数据
     *
     * @param product 商品JSON对象
     * @param listTaskId 列表任务ID
     * @param createTime 创建时间
     * @return 页面任务实体
     */
    private AmazonPageTaskEntity parseProductData(JSONObject product, Integer listTaskId, Date createTime) {
        try {
            AmazonPageTaskEntity task = new AmazonPageTaskEntity();
            
            // 基本信息
            task.setListTaskId(listTaskId);
            task.setCreatedAt(createTime);
            task.setUpdatedAt(createTime);
            task.setStatus(AmazonConstant.REQUEST_STATUS_INIT);
            task.setRetryCount(0);
            
            // ASIN - 从URL或直接字段中提取
            String asin = extractAsin(product);
            if (StrXhUtil.isBlank(asin)) {
                log.warn("无法提取ASIN，跳过该商品: {}", product.toJSONString());
                return null;
            }
            task.setEntryAsin(asin);
            
            // URL
            String url = product.getString("url");
            if (StrXhUtil.isNotBlank(url)) {
                // 确保URL是完整的Amazon URL
                if (!url.startsWith("http")) {
                    url = "https://www.amazon.com" + url;
                }
                task.setUrl(url);
            }
            
            // 标题
            String title = product.getString("title");
            if (StrXhUtil.isNotBlank(title)) {
                task.setListPageTitle(title.trim());
            }
            
            // 价格处理
            parsePrice(product, task);
            
            // 评分和评论数
            parseRatingAndReviews(product, task);
            
            // 图片URL
            String imageUrl = product.getString("image_url");
            if (StrXhUtil.isNotBlank(imageUrl)) {
                task.setListPageMainImageUrl(imageUrl);
            }
            
            // Prime信息
            String primeInfo = product.getString("prime_info");
            if (StrXhUtil.isNotBlank(primeInfo)) {
                task.setListPagePrimeInfo(primeInfo);
            }
            
            // 是否赞助商品
            Boolean isSponsored = product.getBoolean("is_sponsored");
            if (isSponsored != null) {
                task.setIsSponsored(isSponsored);
            }
            
            // 购买数量
            Integer boughtNum = product.getInteger("bought_num");
            if (boughtNum != null) {
                task.setListBoughtNum(boughtNum);
            }
            
            return task;
            
        } catch (Exception e) {
            log.error("解析单个商品数据失败: {}", product.toJSONString(), e);
            return null;
        }
    }

    /**
     * 提取ASIN
     *
     * @param product 商品JSON对象
     * @return ASIN
     */
    private String extractAsin(JSONObject product) {
        // 首先尝试直接获取asin字段
        String asin = product.getString("asin");
        if (StrXhUtil.isNotBlank(asin)) {
            return asin;
        }
        
        // 从URL中提取ASIN
        String url = product.getString("url");
        if (StrXhUtil.isNotBlank(url)) {
            // Amazon ASIN通常在URL中的格式: /dp/ASIN/ 或 /gp/product/ASIN/
            Pattern pattern = Pattern.compile("/(dp|gp/product)/([A-Z0-9]{10})/");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return matcher.group(2);
            }
        }
        
        return null;
    }

    /**
     * 解析价格信息
     *
     * @param product 商品JSON对象
     * @param task 任务实体
     */
    private void parsePrice(JSONObject product, AmazonPageTaskEntity task) {
        // 当前价格
        String priceStr = product.getString("price");
        if (StrXhUtil.isNotBlank(priceStr)) {
            BigDecimal price = parseDecimalFromString(priceStr);
            if (price != null) {
                task.setListPagePrice(price);
            }
        }
        
        // 原价
        String originalPriceStr = product.getString("original_price");
        if (StrXhUtil.isNotBlank(originalPriceStr)) {
            BigDecimal originalPrice = parseDecimalFromString(originalPriceStr);
            if (originalPrice != null) {
                task.setListOrginPrice(originalPrice);
            }
        }
    }

    /**
     * 解析评分和评论数
     *
     * @param product 商品JSON对象
     * @param task 任务实体
     */
    private void parseRatingAndReviews(JSONObject product, AmazonPageTaskEntity task) {
        // 评分
        String ratingStr = product.getString("rating");
        if (StrXhUtil.isNotBlank(ratingStr)) {
            BigDecimal rating = parseDecimalFromString(ratingStr);
            if (rating != null) {
                task.setListPageRating(rating);
            }
        }
        
        // 评论数
        String reviewCountStr = product.getString("review_count");
        if (StrXhUtil.isNotBlank(reviewCountStr)) {
            Integer reviewCount = parseIntegerFromString(reviewCountStr);
            if (reviewCount != null) {
                task.setListPageReviewCount(reviewCount);
            }
        }
    }

    /**
     * 从字符串中解析BigDecimal
     *
     * @param str 字符串
     * @return BigDecimal值
     */
    private BigDecimal parseDecimalFromString(String str) {
        if (StrXhUtil.isBlank(str)) {
            return null;
        }
        
        try {
            // 移除货币符号和逗号
            String cleanStr = str.replaceAll("[^0-9.]", "");
            if (StrXhUtil.isNotBlank(cleanStr)) {
                return new BigDecimal(cleanStr);
            }
        } catch (Exception e) {
            log.warn("解析BigDecimal失败: {}", str);
        }
        
        return null;
    }

    /**
     * 从字符串中解析Integer
     *
     * @param str 字符串
     * @return Integer值
     */
    private Integer parseIntegerFromString(String str) {
        if (StrXhUtil.isBlank(str)) {
            return null;
        }
        
        try {
            // 移除非数字字符
            String cleanStr = str.replaceAll("[^0-9]", "");
            if (StrXhUtil.isNotBlank(cleanStr)) {
                return Integer.parseInt(cleanStr);
            }
        } catch (Exception e) {
            log.warn("解析Integer失败: {}", str);
        }
        
        return null;
    }
}
