[2025-06-15 17:24:10.862] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.875] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.882] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.966] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.984] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.007] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.013] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.017] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.021] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.036] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.039] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.041] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.044] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.048] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.050] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.053] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.055] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.073] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.092] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.094] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:16.761] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 17:24:16.763] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 17:24:16.768] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 17:24:16.775] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 17:24:16.788] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 17:24:25.149] [WARN ] [main] [o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext] [?] [?] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'permissionAdminAspect': Unsatisfied dependency expressed through field 'organizeRelationService': Error creating bean with name 'organizeRelationServiceImpl': Unsatisfied dependency expressed through field 'roleService': Error creating bean with name 'roleServiceImpl': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userRelationService': Error creating bean with name 'userRelationServiceImpl': Unsatisfied dependency expressed through field 'synThirdInfoService': Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
[2025-06-15 17:48:23.470] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.486] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.494] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.597] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.617] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.632] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.636] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.643] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.647] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.662] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.664] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.667] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.669] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.672] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.674] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.676] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.678] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.696] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.714] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.716] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:27.641] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 17:48:27.642] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 17:48:27.643] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 17:48:27.644] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 17:48:27.647] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 17:49:15.649] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 17:50:00.801] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 17:50:00.908] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 17:50:00.986] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 17:50:01.060] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30988 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 17:55:00.134] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 17:55:18.893] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30980 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:07:13.213] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:07:20.476] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30985 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:07:39.602] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 18:07:46.925] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:46.937] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:46.945] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.022] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.039] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.053] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.056] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.060] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.063] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.073] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.075] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.077] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.079] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.082] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.083] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.086] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.087] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.098] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.121] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.122] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:50.856] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 18:07:50.856] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 18:07:50.857] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 18:07:50.859] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 18:07:50.863] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 18:08:39.825] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 18:10:00.457] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:10:00.543] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:10:00.585] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:10:00.657] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30985 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:15:00.087] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:15:00.159] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31006 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:20:00.085] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:20:00.232] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30988 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:25:00.178] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:25:00.317] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30999 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:30:00.137] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:30:03.209] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30982 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:37:15.036] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:37:17.619] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30986 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:40:00.168] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:40:01.301] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:40:01.396] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31012 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:40:57.337] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 18:41:04.789] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.801] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.808] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.891] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.910] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.926] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.930] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.933] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.936] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.948] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.950] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.952] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.954] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.957] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.959] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.962] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.963] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.974] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.993] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.994] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:08.872] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 18:41:08.873] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 18:41:08.873] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 18:41:08.875] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 18:41:08.879] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 18:41:57.583] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 18:45:04.085] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:45:04.152] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:45:04.207] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:45:04.255] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30986 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
